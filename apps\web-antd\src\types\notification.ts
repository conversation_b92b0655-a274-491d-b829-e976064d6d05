/**
 * 消息通知模块类型定义
 * 基于后端API设计，适配Vue3 + TypeScript + Ant Design Vue技术栈
 */

// 通知类型枚举
export enum NotificationType {
  Info = 0,
  Warning = 1,
  Error = 2,
  Success = 3,
}

// 通知分类枚举
export enum NotificationCategory {
  System = 0,    // 系统通知
  Business = 1,  // 业务通知
  Security = 2,  // 安全通知
  Personal = 3,  // 个人通知
}

// 通知优先级枚举
export enum NotificationPriority {
  Low = 0,
  Normal = 1,
  High = 2,
  Critical = 3,
}

// 通知状态枚举
export enum NotificationStatus {
  Unread = 0,    // 未读
  Read = 1,      // 已读
  Archived = 2,  // 已归档
}

// 通知内容接口
export interface NotificationContent {
  title: string;
  message: string;
  details?: string;
  data?: string;
}

// 通知主体接口
export interface Notification {
  id: string;
  content: NotificationContent;
  type: NotificationType;
  category: NotificationCategory;
  priority: NotificationPriority;
  status: NotificationStatus;
  recipients: string[];
  expiresAt?: string;
  sourceType?: string;
  sourceId?: string;
  createdAt: string;
  readAt?: string;
}

// 通知模板变量接口
export interface TemplateVariable {
  name: string;
  displayName: string;
  isRequired: boolean;
  defaultValue?: string;
}

// 通知模板接口
export interface NotificationTemplate {
  id: string;
  code: string;
  name: string;
  title: string;
  content: string;
  type: NotificationType;
  category: NotificationCategory;
  priority: NotificationPriority;
  isActive: boolean;
  variables: TemplateVariable[];
  createdAt: string;
}

// Pinia状态管理接口
export interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  hasMore: boolean;
  filters: {
    status?: NotificationStatus;
    category?: NotificationCategory;
    type?: NotificationType;
  };
}

// API请求类型
export interface CreateNotificationRequest {
  userId?: string;
  title: string;
  content: string;
  type: NotificationType;
  category: NotificationCategory;
  priority?: NotificationPriority;
  sourceType?: string;
  sourceId?: string;
  data?: string;
  expiresAt?: string;
}

// 通过模板创建通知请求
export interface CreateNotificationFromTemplateRequest {
  templateCode: string;
  userId?: string;
  parameters?: Record<string, string>;
}

// 获取通知列表请求参数
export interface GetNotificationsRequest {
  status?: NotificationStatus;
  category?: NotificationCategory;
  type?: NotificationType;
  skip?: number;
  take?: number;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  hasMore: boolean;
}

// 未读数量响应
export interface UnreadCountResponse {
  unreadCount: number;
}

// SignalR事件数据接口
export interface SignalRNotificationData {
  userId: string;
  notification: Notification;
}

export interface SignalRUnreadCountData {
  userId: string;
  unreadCount: number;
}

// 组件Props接口
export interface NotificationCenterProps {
  maxHeight?: number;
  showCategories?: boolean;
  autoRefresh?: boolean;
}

export interface NotificationListProps {
  notifications: Notification[];
  loading?: boolean;
  hasMore?: boolean;
}

export interface NotificationItemProps {
  notification: Notification;
  showActions?: boolean;
}

// 事件回调接口
export interface NotificationEvents {
  onRead: (notification: Notification) => void;
  onMarkAllRead: () => void;
  onLoadMore: () => void;
  onRefresh: () => void;
  onClear: () => void;
}

// 通知配置接口
export interface NotificationConfig {
  signalR: {
    hubUrl: string;
    reconnectDelay: number;
    maxReconnectAttempts: number;
  };
  api: {
    baseUrl: string;
    endpoints: {
      notifications: string;
      templates: string;
      unreadCount: string;
    };
  };
  cache: {
    ttl: number;
    maxSize: number;
  };
  ui: {
    pageSize: number;
    maxNotifications: number;
    autoMarkReadDelay: number;
  };
}

// 错误处理接口
export interface NotificationError {
  code: string;
  message: string;
  details?: any;
}

// 扩展现有的NotificationItem类型以兼容vben框架
export interface VbenNotificationItem {
  avatar: string;
  date: string;
  isRead?: boolean;
  message: string;
  title: string;
  // 扩展字段
  id?: string;
  type?: NotificationType;
  category?: NotificationCategory;
  priority?: NotificationPriority;
}

// 类型工具函数
export type NotificationTypeKeys = keyof typeof NotificationType;
export type NotificationCategoryKeys = keyof typeof NotificationCategory;
export type NotificationPriorityKeys = keyof typeof NotificationPriority;
export type NotificationStatusKeys = keyof typeof NotificationStatus;
