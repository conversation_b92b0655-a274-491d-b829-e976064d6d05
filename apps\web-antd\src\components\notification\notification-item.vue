<template>
  <div
    class="notification-item"
    :class="{
      'notification-item--unread': isUnread,
      'notification-item--clickable': clickable,
      'notification-item--high-priority': isHighPriority,
    }"
    @click="handleClick"
  >
    <!-- 未读标识 -->
    <div v-if="isUnread" class="notification-item__unread-dot" />

    <!-- 头像 -->
    <div v-if="showAvatar" class="notification-item__avatar">
      <Avatar
        :src="avatarSrc"
        :size="40"
        :style="{ backgroundColor: typeConfig.color }"
      >
        <template #icon>
          <component :is="typeIcon" />
        </template>
      </Avatar>
    </div>

    <!-- 内容区域 -->
    <div class="notification-item__content">
      <!-- 标题和时间 -->
      <div class="notification-item__header">
        <div class="notification-item__title">
          {{ notification.content.title }}
        </div>
        <div v-if="showTime" class="notification-item__time">
          {{ formattedTime }}
        </div>
      </div>

      <!-- 消息内容 -->
      <div class="notification-item__message">
        {{ notification.content.message }}
      </div>

      <!-- 底部信息 -->
      <div class="notification-item__footer">
        <!-- 分类标签 -->
        <Tag
          v-if="showCategory"
          :color="categoryConfig.color"
          size="small"
        >
          {{ categoryConfig.name }}
        </Tag>

        <!-- 优先级标签 -->
        <Tag
          v-if="priorityConfig.weight > 2"
          :color="priorityConfig.color"
          size="small"
        >
          {{ priorityConfig.name }}
        </Tag>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="showActions" class="notification-item__actions">
      <Space direction="vertical" size="small">
        <Button
          v-if="isUnread"
          type="text"
          size="small"
          @click.stop="handleMarkRead"
        >
          <CheckCircle class="w-4 h-4" />
        </Button>
        <Button
          type="text"
          size="small"
          danger
          @click.stop="handleDelete"
        >
          <X class="w-4 h-4" />
        </Button>
      </Space>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Notification, NotificationStatus } from '#/types/notification';
import type { NotificationItemEvents, NotificationItemProps } from './types';

import { computed } from 'vue';

import { Avatar, Button, Space, Tag } from 'ant-design-vue';
import { CheckCircle, X } from 'lucide-vue-next';

import {
  getDefaultAvatar,
  getNotificationCategoryConfig,
  getNotificationPriorityConfig,
  getNotificationTypeConfig,
} from '#/config/notification';

// Props定义
const props = withDefaults(defineProps<NotificationItemProps>(), {
  showActions: true,
  showAvatar: true,
  showTime: true,
  showCategory: true,
  clickable: true,
});

// 事件定义
const emit = defineEmits<NotificationItemEvents>();

// 计算属性
const isUnread = computed(() => props.notification.status === 0);
const isHighPriority = computed(() => props.notification.priority >= 2);

const typeConfig = computed(() => getNotificationTypeConfig(props.notification.type));
const categoryConfig = computed(() => getNotificationCategoryConfig(props.notification.category));
const priorityConfig = computed(() => getNotificationPriorityConfig(props.notification.priority));

const typeIcon = computed(() => {
  const iconMap = {
    Info: 'info-circle',
    AlertTriangle: 'exclamation-triangle',
    XCircle: 'times-circle',
    CheckCircle: 'check-circle',
  };
  return iconMap[typeConfig.value.icon as keyof typeof iconMap] || 'info-circle';
});

const avatarSrc = computed(() => {
  // 如果通知有自定义头像，使用自定义头像，否则使用默认头像
  return getDefaultAvatar(props.notification.category);
});

const formattedTime = computed(() => {
  const date = new Date(props.notification.createdAt);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 1分钟内
  if (diff < 60000) return '刚刚';
  // 1小时内
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  // 1天内
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  // 1周内
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;
  
  // 超过1周显示具体日期
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
});

// 事件处理
function handleClick() {
  if (!props.clickable) return;
  
  emit('click', props.notification);
  
  // 如果是未读通知，自动标记为已读
  if (isUnread.value) {
    handleMarkRead();
  }
}

function handleMarkRead() {
  emit('mark-read', props.notification.id);
}

function handleDelete() {
  emit('delete', props.notification.id);
}
</script>

<style scoped>
.notification-item {
  @apply relative flex items-start gap-3 p-3 border-b border-gray-100 transition-all duration-200 cursor-pointer;
}

.notification-item:hover {
  @apply bg-gray-50;
}

.notification-item--unread {
  @apply bg-blue-50 border-l-4 border-l-blue-500;
}

.notification-item--clickable:hover {
  @apply bg-gray-50;
}

.notification-item--high-priority {
  @apply border-l-4 border-l-red-500;
}

.notification-item__unread-dot {
  @apply absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full;
}

.notification-item__avatar {
  @apply flex-shrink-0;
}

.notification-item__content {
  @apply flex-1 min-w-0;
}

.notification-item__header {
  @apply flex items-start justify-between gap-2 mb-1;
}

.notification-item__title {
  @apply font-medium text-gray-900 text-sm leading-5 flex-1;
}

.notification-item__time {
  @apply text-xs text-gray-500 flex-shrink-0;
}

.notification-item__message {
  @apply text-sm text-gray-600 leading-5 mb-2 line-clamp-2;
}

.notification-item__footer {
  @apply flex items-center gap-2;
}

.notification-item__actions {
  @apply flex-shrink-0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-item {
    @apply p-2 gap-2;
  }
  
  .notification-item__title {
    @apply text-xs;
  }
  
  .notification-item__message {
    @apply text-xs;
  }
  
  .notification-item__time {
    @apply text-xs;
  }
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
