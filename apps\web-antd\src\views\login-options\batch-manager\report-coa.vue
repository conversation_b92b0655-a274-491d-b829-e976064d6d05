<script setup lang="ts">
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { onMounted, ref, watch } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import { Button, message, Space } from 'ant-design-vue';

import { downloadWitlabFile } from '#/api/core/witlab';
import {
  checkRolePermissionApi,
  findDefaultCoATemplateApi,
  getCoaReportApi,
  getReportStarDocIdApi,
  printSignaturePDFApi,
  reGenerateCOA_NEWApi,
  reGenerateCOAApi,
  retireReportApi,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import PrintHistoryForm from '#/views/common-apps/common-app/print-history.vue';

import {
  useReportCOAColumns,
  useReportCOAFilterSchema,
} from './batch-manager-data';
import SelectReportForm from './select-report-model.vue';

const props = defineProps<{
  currentTestRow: BatcheManagerApi.Batches | null;
  mode: string;
  stepCode: string;
}>();
const sUserRole = ref<string>('');

const userStore = useUserStore();
if (userStore.userInfo) {
  sUserRole.value = userStore.userInfo.roleCode as string;
}

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useReportCOAColumns();
const filterSchema = useReportCOAFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  let sWhr = ` and REPORTS.STATUS = '${props.stepCode}'`;
  if (props.stepCode === 'Manager') {
    sWhr = '';
  }
  const data = await getCoaReportApi(
    props.currentTestRow.BATCHID,
    sWhr,
    props.mode,
    props.stepCode,
  );
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<BatcheManagerApi.ReportCOA>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

// 生成COA
const [SelectReportFormModal, selectReportFormModalApi] = useVbenModal({
  connectedComponent: SelectReportForm,
});

// 生成COA
async function onCreateCoA() {
  if (!props.currentTestRow) return;
  const sBatchId = props.currentTestRow.BATCHID;
  const rv = await findDefaultCoATemplateApi(sBatchId);
  if (rv && rv[0]) {
    selectReportFormModalApi
      .setData({
        BATCHREPORTTYPE: 'Single',
        BATCHID: sBatchId,
        REPORT_TEMPLATE: rv[1],
        REPORTWAY: '',
        MODE: props.mode,
      })
      .open();
  } else {
    selectReportFormModalApi
      .setData({
        BATCHREPORTTYPE: 'Single',
        BATCHID: sBatchId,
        REPORT_TEMPLATE: '',
        REPORTWAY: '',
        MODE: props.mode,
      })
      .open();
  }
}

// 重新生成COA
async function onReGenerateCoA() {
  if (CurrentRow.value === null) return;

  gridApi.setLoading(true);

  const aOrigrec = ref<number[]>([]);
  const sOrigrec = CurrentRow.value.ORIGREC;
  const status = CurrentRow.value.STATUS;

  aOrigrec.value.push(sOrigrec);

  if (status === 'Retired') {
    const res = await reGenerateCOA_NEWApi(aOrigrec.value);
    if (!res) {
      message.warn($t('login-options.tpCoA.fail102'));
      return;
    }
  } else {
    await reGenerateCOAApi(aOrigrec.value);
  }
  message.success('重新生成成功');
  gridApi.setLoading(false);
  onRefresh();
}

// 浏览COA
async function onBrowseCOA() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const sStardocId = await getReportStarDocIdApi(CurrentRow.value.ORIGREC);
  if (sStardocId === null || sStardocId === '') return;
  const sFileName = CurrentRow.value.REPORTNO;
  downloadWitlabFile(sStardocId, sFileName);
}

// 打印COA
const [PrintHistoryFormModal, printHistoryFormModalApi] = useVbenModal({
  connectedComponent: PrintHistoryForm,
});
// 打印COA
async function onPrintCOA() {
  if (CurrentRow.value === null) {
    return;
  }
  const sOrigrec = CurrentRow.value.ORIGREC;
  const sStardocId = await getReportStarDocIdApi(sOrigrec);
  if (sStardocId === null || sStardocId === '') return;

  const res = await checkRolePermissionApi(sUserRole.value);
  if (!res) {
    message.warn($t('login-options.tpCoA.NoPrintPermission'));
    return;
  }

  const aRet = await printSignaturePDFApi(sStardocId);
  if (aRet[0]) {
    printHistoryFormModalApi
      .setData({
        ORIGREC: sOrigrec,
        STARDOCID: sStardocId,
      })
      .open();
  } else {
    message.warn($t(`login-options.tpCoA.${aRet[1]}`));
  }
}

// 作废COA
async function onRetireCOA() {
  if (!CurrentRow.value) return;
  const aReportNo = ref<string[]>([]);

  const sReportNo = CurrentRow.value.REPORTNO;
  aReportNo.value.push(sReportNo);

  try {
    await confirm({
      title: '作废',
      content: `请确认是否废弃所选报告？`,
      icon: 'warning',
      centered: false,
    });

    await retireReportApi(aReportNo.value, '');
    message.success('操作成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 合并COA
async function onMultiBatchCOA() {
  if (!props.currentTestRow) return;
  const sBatchId = props.currentTestRow.BATCHID;
  const rv = await findDefaultCoATemplateApi(sBatchId);
  if (rv && rv[0]) {
    selectReportFormModalApi
      .setData({
        BATCHREPORTTYPE: 'Multi',
        BATCHID: sBatchId,
        REPORT_TEMPLATE: rv[1],
        REPORTWAY: '最终报告',
      })
      .open();
  } else {
    selectReportFormModalApi
      .setData({
        BATCHREPORTTYPE: 'Multi',
        BATCHID: sBatchId,
        REPORT_TEMPLATE: '',
        REPORTWAY: '最终报告',
      })
      .open();
  }
}

const submitBtn = ref<string>('');
const isShowRetireBtn = ref<boolean>(false);
const isShowReGenerateCoaBtn = ref<boolean>(false);
const isShowSubmitBtn = ref<boolean>(true);
const isShowPrintBtn = ref(false);

// 首次加载执行
onMounted(() => {
  if (props.mode === 'MainFlowCOA') {
    isShowPrintBtn.value = true;
    switch (props.stepCode) {
      case 'Approval': {
        submitBtn.value = '审核COA';

        break;
      }
      case 'Draft': {
        submitBtn.value = '提交COA';
        isShowRetireBtn.value = true;
        isShowReGenerateCoaBtn.value = true;
        break;
      }
      case 'Manager': {
        isShowRetireBtn.value = true;
        isShowSubmitBtn.value = false;
        break;
      }
      case 'Release': {
        submitBtn.value = '批准COA';
        break;
      }
      // No default
    }
  }
  // 批次发布
  if (
    props.mode === 'Release' ||
    props.mode === 'LevelPass' ||
    props.mode === 'LevelApprove'
  ) {
    isShowSubmitBtn.value = false;
  } else if (props.mode === 'View') {
    isShowPrintBtn.value = true;
  }
});

const isPrintCoaDisabled = ref<boolean>(false);
const isRetireCoaDisabled = ref<boolean>(false);
const isSubmitCoaDisabled = ref<boolean>(false);
const isReGenerateCoaCoaDisabled = ref<boolean>(false);

// 监控选中行
watch(
  () => CurrentRow.value?.STATUS,
  (status) => {
    isRetireCoaDisabled.value = status !== 'Done' && status !== 'Draft';
    isPrintCoaDisabled.value = status === 'Retired';
    if (props.mode === 'MainFlowCOA' && props.stepCode === 'Draft') {
      isShowSubmitBtn.value = status === 'Draft';
      isShowReGenerateCoaBtn.value = status === 'Draft' || status === 'Retired';
    }
    isSubmitCoaDisabled.value = status === 'Retired' || status === 'Done';
    isReGenerateCoaCoaDisabled.value =
      status !== 'Draft' && status !== 'Retired';
  },
);
</script>

<template>
  <SelectReportFormModal @success="onRefresh" />
  <PrintHistoryFormModal />
  <Grid class="h-full w-full">
    <template #toolbar-actions>
      <Space>
        <Button
          type="primary"
          @click="onCreateCoA"
          v-if="props.mode === 'MainFlowCOA' && props.stepCode === 'Draft'"
        >
          {{ $t('login-options.tpCoA.createCOA') }}
        </Button>
        <Button
          type="default"
          @click="onReGenerateCoA"
          v-if="isShowReGenerateCoaBtn"
          :disabled="isReGenerateCoaCoaDisabled"
        >
          {{ $t('login-options.tpCoA.reGenerateCOA') }}
        </Button>
        <Button type="default" @click="onBrowseCOA">
          {{ $t('login-options.tpCoA.browseCOA') }}
        </Button>
        <Button
          type="default"
          @click="onPrintCOA"
          :disabled="isPrintCoaDisabled"
          v-if="isShowPrintBtn"
        >
          {{ $t('login-options.tpCoA.printCOA') }}
        </Button>
        <Button
          type="default"
          v-if="isShowSubmitBtn"
          :disabled="isSubmitCoaDisabled"
        >
          {{ submitBtn }}
        </Button>
        <Button
          type="default"
          @click="onRetireCOA"
          v-if="isShowRetireBtn"
          :disabled="isRetireCoaDisabled"
        >
          {{ $t('login-options.tpCoA.retireCOA') }}
        </Button>
        <Button
          type="default"
          @click="onMultiBatchCOA"
          v-if="props.mode === 'MainFlowCOA' && props.stepCode === 'Draft'"
        >
          {{ $t('login-options.tpCoA.multiBatchCOA') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent(row)">
          {{ $t('login-options.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('login-options.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('login-options.edit') }}
        </Button>
      </template>
    </template>
  </Grid>
</template>
