<script lang="ts" setup>
import type { CommonAppApi } from '#/api/common-apps/common-app';

import { ref } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import {
  addTempRawItemsApi,
  deleteTempRawItemsApi,
  deleteTempRawItemsByBatchIdApi,
  dsGetRawCoAApi,
  dsGetRawELNApi,
  dsGetRawPDFApi,
  dsGetRawRequestFormApi,
  dsGetTempRawItemApi,
  generateRR4BatchApi,
  generateRR4BatchByDocIdsApi,
  getRequestFormByBatchApi,
} from '#/api/common-apps/common-app';
import { downloadWitlabFile } from '#/api/core/witlab';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useRawRecordCOAColumns,
  useRawRecordCOAFilterSchema,
  useRawRecordELNColumns,
  useRawRecordELNFilterSchema,
  useRawRecordPDFColumns,
  useRawRecordPDFFilterSchema,
  useRawRecordRequestColumns,
  useRawRecordRequestFilterSchema,
  useRawRecordTempDocColumns,
  useRawRecordTempDocFilterSchema,
} from './data';

// 定义组件事件
const emit = defineEmits(['success']);
const activeKey = ref('tpCoA');
const sBatchId = ref<number>(0);

// COA
const coaColums = useRawRecordCOAColumns();
const coaFilterSchema = useRawRecordCOAFilterSchema();

const coaQueryData = async () => {
  return dsGetRawCoAApi(sBatchId.value);
};
const coaGirdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid: CoAGrid,
  gridApi: coAGridApi,
  SelectedCheckRows: coaSelectedCheckRows,
} = useLimsGridsConfig<CommonAppApi.RawRecordCOA>(
  coaColums,
  coaFilterSchema,
  coaQueryData,
  coaGirdOption,
);

function onCoaRefresh() {
  coAGridApi.query(); // 主动触发一次查询
}

// ELN
const elnColums = useRawRecordELNColumns();
const elnFilterSchema = useRawRecordELNFilterSchema();

const elnQueryData = async () => {
  return dsGetRawELNApi(sBatchId.value);
};
const elnGirdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid: ElnGrid,
  gridApi: elnGridApi,
  SelectedCheckRows: elnSelectedCheckRows,
} = useLimsGridsConfig<CommonAppApi.RawRecordELN>(
  elnColums,
  elnFilterSchema,
  elnQueryData,
  elnGirdOption,
);

function onElnRefresh() {
  elnGridApi.query(); // 主动触发一次查询
}

// 请验单
const requestColums = useRawRecordRequestColumns();
const requestFilterSchema = useRawRecordRequestFilterSchema();

const requestQueryData = async () => {
  return dsGetRawRequestFormApi(sBatchId.value);
};
const requestGirdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid: RequestGrid,
  gridApi: requestGridApi,
  SelectedCheckRows: requestSelectedCheckRows,
  CurrentRow: requestCurrentRow,
} = useLimsGridsConfig<CommonAppApi.RawRecordRequest>(
  requestColums,
  requestFilterSchema,
  requestQueryData,
  requestGirdOption,
);

function onRequestRefresh() {
  requestGridApi.query(); // 主动触发一次查询
}

// 原厂报告
const pdfColums = useRawRecordPDFColumns();
const pdfFilterSchema = useRawRecordPDFFilterSchema();

const pdfQueryData = async () => {
  return dsGetRawPDFApi(sBatchId.value);
};
const pdfGirdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid: PdfGrid,
  gridApi: pdfGridApi,
  SelectedCheckRows: pdfSelectedCheckRows,
} = useLimsGridsConfig<CommonAppApi.RawRecordPDF>(
  pdfColums,
  pdfFilterSchema,
  pdfQueryData,
  pdfGirdOption,
);

function onPdfRefresh() {
  pdfGridApi.query(); // 主动触发一次查询
}

// 临时
const tmpColums = useRawRecordTempDocColumns();
const tmpFilterSchema = useRawRecordTempDocFilterSchema();

const tmpQueryData = async () => {
  return dsGetTempRawItemApi(sBatchId.value);
};
const tmpGirdOption = {
  formConfig: {
    enabled: false,
  },
};

const { Grid: TmpDocGrid, gridApi: tmpDocGridApi } =
  useLimsGridsConfig<CommonAppApi.RawRecordTempDoc>(
    tmpColums,
    tmpFilterSchema,
    tmpQueryData,
    tmpGirdOption,
  );

function onTmpDocRefresh() {
  tmpDocGridApi.query(); // 主动触发一次查询
}

// 选中
async function onTmpDocSelect() {
  switch (activeKey.value) {
    case 'tpCoA': {
      if (coaSelectedCheckRows.value.length === 0) return;
      const aItems = await getSelectedRows(coaSelectedCheckRows.value);
      if (aItems.length === 0) return;
      await addTempRawItemsApi(aItems);
      onCoaRefresh();
      break;
    }
    case 'tpEln': {
      if (elnSelectedCheckRows.value.length === 0) return;
      const aItems = await getSelectedRows(elnSelectedCheckRows.value);
      if (aItems.length === 0) return;
      await addTempRawItemsApi(aItems);
      onElnRefresh();
      break;
    }
    case 'tpPdf': {
      if (pdfSelectedCheckRows.value.length === 0) return;
      const aItems = await getSelectedRows(pdfSelectedCheckRows.value);
      if (aItems.length === 0) return;
      await addTempRawItemsApi(aItems);
      onRequestRefresh();
      break;
    }

    case 'tpRequest': {
      if (requestSelectedCheckRows.value.length === 0) return;
      const aItems = await getSelectedRows(requestSelectedCheckRows.value);
      if (aItems.length === 0) return;
      await addTempRawItemsApi(aItems);
      onRequestRefresh();
      break;
    }
  }
  onTmpDocRefresh();
}
// 全选
async function onSelectAll() {
  switch (activeKey.value) {
    case 'tpCoA': {
      coAGridApi.grid.setAllCheckboxRow(true);
      const checkRecords = coAGridApi.grid.getCheckboxRecords();
      coaSelectedCheckRows.value = checkRecords;

      break;
    }
    case 'tpEln': {
      elnGridApi.grid.setAllCheckboxRow(true);
      const checkRecords = elnGridApi.grid.getCheckboxRecords();
      elnSelectedCheckRows.value = checkRecords;
      break;
    }
    case 'tpPdf': {
      pdfGridApi.grid.setAllCheckboxRow(true);
      const checkRecords = pdfGridApi.grid.getCheckboxRecords();
      pdfSelectedCheckRows.value = checkRecords;
      break;
    }

    case 'tpRequest': {
      requestGridApi.grid.setAllCheckboxRow(true);
      const checkRecords = requestGridApi.grid.getCheckboxRecords();
      requestSelectedCheckRows.value = checkRecords;
      break;
    }
  }
}

// 取消全选
async function onUnSelectAll() {
  switch (activeKey.value) {
    case 'tpCoA': {
      coAGridApi.grid.clearCheckboxRow();
      break;
    }
    case 'tpEln': {
      elnGridApi.grid.clearCheckboxRow();
      break;
    }
    case 'tpPdf': {
      pdfGridApi.grid.clearCheckboxRow();
      break;
    }

    case 'tpRequest': {
      requestGridApi.grid.clearCheckboxRow();
      break;
    }
  }
}

async function getSelectedRows(SelectedCheckRows: any[]) {
  const aRows = ref<any[][]>([]);
  if (SelectedCheckRows.length === 0) {
    message.warn($t('common-apps.commonApp.PleaseSelectItems'));
    return [];
  }
  for (const item of SelectedCheckRows) {
    aRows.value.push([
      sBatchId.value.toString(),
      item.ITEMTYPE,
      item.STARDOC_ID,
      item.DESCRIPTION,
    ]);
  }
  return aRows.value;
}

// 生成
async function onGenerateReqForm() {
  if (sBatchId.value === 0) return;
  const aRet = await getRequestFormByBatchApi(sBatchId.value);
  if (aRet.length === 0) {
    message.warn($t('common-apps.commonApp.GenerateRequestFormFailed'));
    return;
  }
  onRequestRefresh();
}

// 预览
async function onViewReqForm() {
  if (!requestCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = requestCurrentRow.value.STARDOC_ID;
  const fileName = requestCurrentRow.value.DESCRIPTION;
  downloadWitlabFile(docId, fileName);
}

// 默认方式合成
async function onRawDefault() {
  const aBatch = ref<number[]>([]);
  if (sBatchId.value === 0) return;
  try {
    modalApi.lock();
    aBatch.value.push(sBatchId.value);
    const aRet = await generateRR4BatchApi(aBatch.value);
    if (!aRet) {
      message.warn(aRet[1]);
    }

    emit('success');
    message.success('合成成功');
    modalApi.close();
  } catch {
    message.error('合成失败');
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}

// 选择合成
async function onConfirm() {
  const arrFile = ref<string[]>([]);
  if (sBatchId.value === 0) return;
  try {
    modalApi.lock();

    if (tmpDocGridApi.grid.getData().length === 0) return;
    for (const item of tmpDocGridApi.grid.getData()) {
      arrFile.value.push(item.STARDOC_ID);
    }
    const aRet = await generateRR4BatchByDocIdsApi(
      sBatchId.value,
      arrFile.value,
    );
    if (!aRet) {
      message.warn(aRet[1]);
    }

    emit('success');
    message.success('合成成功');
    modalApi.close();
  } catch {
    message.error('合成失败');
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}

// 取消选择
async function onCancleSelect() {
  const aOrigrec: number[] = tmpDocGridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据');
  }

  await confirm({
    title: '取消选择',
    content: `确定取消选择吗？？`,
    icon: 'warning',
    centered: false,
  });

  await deleteTempRawItemsApi(aOrigrec);
  onTmpDocRefresh();
}

async function cleanTempDoc() {
  if (sBatchId.value === 0) return;
  await deleteTempRawItemsByBatchIdApi(sBatchId.value);
}

// 获取Modal实例
const [Modal, modalApi] = useVbenModal({
  title: '合成原始记录',
  showConfirmButton: false,
  onConfirm() {
    modalApi.close();
  },
  onCancel() {
    cleanTempDoc();
    modalApi.close();
  },

  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any>>();
      sBatchId.value = data.BATCHID;
      onCoaRefresh();
      onElnRefresh();
      onRequestRefresh();
      onPdfRefresh();
    }
  },
});
</script>

<template>
  <Modal class="h-[800px] w-[1000px]">
    <div class="flex h-full flex-col">
      <div class="h-1/7 w-full">
        <Space>
          <Button type="primary" @click="onTmpDocSelect">
            {{ $t('common-apps.commonApp.select') }}
          </Button>
          <Button type="primary" @click="onSelectAll">
            {{ $t('common-apps.commonApp.selectAll') }}
          </Button>
          <Button type="primary" danger @click="onUnSelectAll">
            {{ $t('common-apps.commonApp.unSelectAll') }}
          </Button>
        </Space>
      </div>
      <div class="h-3/6 w-full">
        <Tabs v-model:active-key="activeKey" class="h-full">
          <TabPane key="tpCoA" tab="CoA报告">
            <CoAGrid class="h-full" />
          </TabPane>
          <TabPane key="tpEln" tab="原始记录及图谱">
            <ElnGrid class="h-full" />
          </TabPane>
          <TabPane key="tpRequest" tab="请验单">
            <RequestGrid class="h-full">
              <template #toolbar-actions>
                <Space>
                  <Button type="default" @click="onGenerateReqForm">
                    {{ $t('common-apps.commonApp.generateReqForm') }}
                  </Button>
                  <Button type="default" @click="onViewReqForm">
                    {{ $t('common-apps.commonApp.viewReqForm') }}
                  </Button>
                </Space>
              </template>
            </RequestGrid>
          </TabPane>
          <TabPane key="tpPdf" tab="原厂报告">
            <PdfGrid class="h-full" />
          </TabPane>
        </Tabs>
      </div>
      <div class="h-2/6 flex-1 bg-white px-5">
        <TmpDocGrid class="h-full">
          <template #toolbar-actions>
            <Space>
              <Button type="default" @click="onCancleSelect">
                {{ $t('common-apps.commonApp.cancleSelect') }}
              </Button>
            </Space>
          </template>
        </TmpDocGrid>
      </div>
    </div>
    <template #prepend-footer>
      <div class="flex justify-end">
        <Space>
          <Button type="primary" @click="onRawDefault">
            {{ $t('common-apps.commonApp.rawDefault') }}
          </Button>
          <Button type="primary" @click="onConfirm">
            {{ $t('common-apps.commonApp.confirm') }}
          </Button>
        </Space>
      </div>
    </template>
  </Modal>
</template>
