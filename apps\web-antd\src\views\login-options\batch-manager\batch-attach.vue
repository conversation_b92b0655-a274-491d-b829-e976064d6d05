<script setup lang="ts">
import type {
  UploadChangeParam,
  UploadFile,
  UploadProps,
} from 'ant-design-vue';

import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref, watch } from 'vue';

import { confirm } from '@vben/common-ui';

import { Button, message, Space, Upload } from 'ant-design-vue';

import {
  downloadWitlabFile,
  getServerTempFilePathByWitlabId,
  UploadWitlabTempFile,
} from '#/api/core/witlab';
import {
  addAttachmentReportApi,
  deleteDocumentation,
  getReportFile,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useBatchAttachColumns,
  useBatchAttachFilterSchema,
} from './batch-manager-data';

const props = defineProps<{
  currentTestRow: BatcheManagerApi.Batches | null;
  mode: string;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);

const fileList = ref<UploadProps['fileList']>([]); // 文件列表
const headers = {
  authorization: 'authorization-text',
};

const colums = useBatchAttachColumns();
const filterSchema = useBatchAttachFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getReportFile(props.currentTestRow.ORIGREC);
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<BatcheManagerApi.BatchAttach>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

const beforeUpload = (file: UploadFile) => {
  fileList.value = [...(fileList.value || []), file];
  return false;
};

// 上传附件
const uploadDocuments = async (info: UploadChangeParam) => {
  if (!props.currentTestRow) return;

  const sOrigrec = props.currentTestRow.ORIGREC;

  if (
    info.file &&
    info.fileList &&
    info.fileList[0] &&
    info.fileList[0].originFileObj
  ) {
    gridApi.setLoading(true);
    const tempPath = await UploadWitlabTempFile(info.fileList[0].originFileObj);
    const tempServerPath = await getServerTempFilePathByWitlabId({
      key: tempPath.AccessKey,
      fileId: tempPath.FileId,
    });
    if (!tempServerPath) {
      message.error($t('commons.uploadFailed'));
      return;
    }

    await addAttachmentReportApi(sOrigrec, info.file.name, tempServerPath);
    gridApi.query();
    gridApi.setLoading(false);
  }
};

// 移除附件
async function onRemove() {
  // 获取选中行
  const doc = gridApi.grid?.getCurrentRecord();
  if (!doc) return;

  const sOrigrec = doc.ORIGREC;
  const sStarDocId = doc.STARDOC_ID;
  try {
    await confirm({
      title: '确认移除',
      content: `确定要移除选中的数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteDocumentation(sOrigrec, sStarDocId);

    message.success('移除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

async function onViewDocuments() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = CurrentRow.value.STARDOC_ID;
  const fileName = CurrentRow.value.ATTACHMENTNAME;
  downloadWitlabFile(docId, fileName);
}
</script>

<template>
  <Grid class="h-full w-full">
    <template #toolbar-actions>
      <Space>
        <Upload
          v-model:file-list="fileList"
          name="file"
          :show-upload-list="false"
          :headers="headers"
          :max-count="1"
          @change="uploadDocuments"
          :before-upload="beforeUpload"
        >
          <Button type="primary" v-if="props.mode !== 'View'">
            {{ $t('ui.actionTitle.create') }}
          </Button>
        </Upload>
        <Button
          type="primary"
          danger
          @click="onRemove"
          v-if="props.mode !== 'View'"
        >
          {{ $t('login-options.remove') }}
        </Button>
        <Button type="default" @click="onViewDocuments">
          {{ $t('login-options.batchManager.viewFile') }}
        </Button>
        <Button type="default">
          {{ $t('login-options.batchManager.btnPicture') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent(row)">
          {{ $t('login-options.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('login-options.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('login-options.edit') }}
        </Button>
      </template>
    </template>
  </Grid>
</template>
