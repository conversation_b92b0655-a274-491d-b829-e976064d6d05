# 消息通知模块

基于Vue3 + TypeScript + Ant Design Vue + SignalR的完整消息通知系统。

## 📋 功能特性

### 🔔 实时通知
- SignalR实时通信，支持即时消息推送
- 自动重连机制，确保连接稳定性
- 角色组订阅，支持按角色推送通知

### 📱 通知中心
- 响应式设计，适配移动端和桌面端
- 未读消息提醒，红点标识和数量显示
- 分类标签，支持按类型筛选通知
- 无限滚动加载，优化大量数据展示

### 🎯 通知管理
- 完整的CRUD操作
- 批量操作支持（标记已读、删除等）
- 通知模板管理
- 数据导出功能

### 🎨 用户体验
- 优雅的动画效果
- 铃铛摇摆提醒
- 自动标记已读
- 时间智能显示

## 🏗️ 架构设计

### 目录结构
```
src/
├── components/notification/          # 通知组件
│   ├── notification-center.vue      # 通知中心主组件
│   ├── notification-list.vue        # 通知列表组件
│   ├── notification-item.vue        # 通知项组件
│   ├── types.ts                     # 组件类型定义
│   └── index.ts                     # 组件导出
├── types/notification.ts            # 通知类型定义
├── config/notification.ts           # 通知配置
├── api/notification/                # API服务
│   ├── index.ts                     # API服务主文件
│   └── types.ts                     # API类型定义
├── services/signalr.ts             # SignalR服务
├── store/notification.ts           # Pinia状态管理
└── views/system/notification/      # 管理页面
    ├── list.vue                    # 通知列表页
    ├── data.ts                     # 表格配置
    └── modules/detail.vue          # 详情弹窗
```

### 技术栈
- **前端框架**: Vue3 + TypeScript + 组合式API
- **UI组件库**: Ant Design Vue
- **状态管理**: Pinia
- **实时通信**: SignalR
- **图标库**: lucide-vue-next
- **HTTP客户端**: @vben/request

## 🚀 快速开始

### 1. 安装依赖
```bash
pnpm add @microsoft/signalr vue-json-pretty
```

### 2. 环境配置
在 `.env` 文件中添加：
```env
# SignalR Hub URL
VITE_SIGNALR_HUB_URL=http://localhost:5000/notificationHub

# API Base URL
VITE_API_BASE_URL=http://localhost:5000/api
```

### 3. 使用通知中心
```vue
<template>
  <NotificationCenter
    :show-categories="true"
    :auto-refresh="true"
    :refresh-interval="30000"
    @notification-click="handleNotificationClick"
  />
</template>

<script setup lang="ts">
import { NotificationCenter } from '#/components/notification';

function handleNotificationClick(notification) {
  console.log('Notification clicked:', notification);
}
</script>
```

### 4. 初始化通知系统
```typescript
import { useNotificationStore } from '#/store/notification';

const notificationStore = useNotificationStore();

// 初始化通知系统
await notificationStore.initialize();
```

## 📡 API接口

### 通知相关接口
- `GET /notifications` - 获取通知列表
- `GET /notifications/{id}` - 获取通知详情
- `GET /notifications/unread-count` - 获取未读数量
- `POST /notifications` - 创建通知
- `PUT /notifications/{id}/read` - 标记已读
- `PUT /notifications/read-all` - 全部标记已读
- `DELETE /notifications/{id}` - 删除通知
- `POST /notifications/batch` - 批量操作

### 模板相关接口
- `GET /notifications/templates` - 获取模板列表
- `POST /notifications/from-template` - 通过模板创建通知

## 🔧 配置说明

### 通知类型
- `Info (0)` - 信息通知
- `Warning (1)` - 警告通知
- `Error (2)` - 错误通知
- `Success (3)` - 成功通知

### 通知分类
- `System (0)` - 系统通知
- `Business (1)` - 业务通知
- `Security (2)` - 安全通知
- `Personal (3)` - 个人通知

### 通知状态
- `Unread (0)` - 未读
- `Read (1)` - 已读
- `Archived (2)` - 已归档

## 🎨 自定义样式

### CSS变量
```css
:root {
  --notification-primary-color: #1890ff;
  --notification-success-color: #52c41a;
  --notification-warning-color: #faad14;
  --notification-error-color: #ff4d4f;
}
```

### 响应式断点
- 移动端: < 768px
- 平板端: 768px - 1024px
- 桌面端: > 1024px

## 🔍 故障排除

### 常见问题

1. **SignalR连接失败**
   - 检查环境变量配置
   - 确认后端服务运行状态
   - 检查网络连接

2. **通知不显示**
   - 检查API接口返回数据格式
   - 确认权限配置正确
   - 查看浏览器控制台错误

3. **实时推送不工作**
   - 检查SignalR连接状态
   - 确认用户组订阅成功
   - 检查后端推送逻辑

### 调试工具
```typescript
// 获取SignalR连接信息
import { signalRService } from '#/services/signalr';
console.log(signalRService.getConnectionInfo());

// 获取通知状态
import { useNotificationStore } from '#/store/notification';
const store = useNotificationStore();
console.log(store.$state);
```

## 📈 性能优化

### 建议配置
- 通知缓存时间: 5分钟
- 最大通知数量: 100条
- 分页大小: 20条
- 自动刷新间隔: 30秒

### 优化策略
- 虚拟滚动处理大量数据
- 防抖处理频繁更新
- 合理的缓存策略
- 懒加载非关键资源

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
