/**
 * 通知状态管理
 * 基于Pinia，集成SignalR实时通信和API服务
 */

import type {
  Notification,
  NotificationCategory,
  NotificationState,
  NotificationStatus,
  NotificationType,
} from '#/types/notification';

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

import { message } from 'ant-design-vue';

import { notificationApi } from '#/api/notification';
import { notificationConfig } from '#/config/notification';
import { signalRService, type SignalREventCallbacks } from '#/services/signalr';

/**
 * 通知状态管理Store
 */
export const useNotificationStore = defineStore('notification', () => {
  // 状态定义
  const notifications = ref<Notification[]>([]);
  const unreadCount = ref(0);
  const loading = ref(false);
  const hasMore = ref(true);
  const initialized = ref(false);
  const filters = ref<{
    status?: NotificationStatus;
    category?: NotificationCategory;
    type?: NotificationType;
  }>({});

  // 计算属性
  const filteredNotifications = computed(() => {
    return notifications.value.filter((notification) => {
      if (filters.value.status !== undefined && notification.status !== filters.value.status) {
        return false;
      }
      if (filters.value.category !== undefined && notification.category !== filters.value.category) {
        return false;
      }
      if (filters.value.type !== undefined && notification.type !== filters.value.type) {
        return false;
      }
      return true;
    });
  });

  const unreadNotifications = computed(() =>
    notifications.value.filter((n) => n.status === NotificationStatus.Unread),
  );

  const notificationsByCategory = computed(() =>
    notifications.value.reduce(
      (acc, notification) => {
        const category = notification.category;
        if (!acc[category]) acc[category] = [];
        acc[category].push(notification);
        return acc;
      },
      {} as Record<number, Notification[]>,
    ),
  );

  const hasUnreadNotifications = computed(() => unreadCount.value > 0);

  // Actions
  /**
   * 初始化通知系统
   */
  async function initialize(): Promise<void> {
    if (initialized.value) return;

    try {
      // 设置SignalR事件回调
      const callbacks: SignalREventCallbacks = {
        onNotificationReceived: handleNewNotification,
        onUnreadCountUpdated: handleUnreadCountUpdate,
        onConnectionStateChanged: (state) => {
          console.log('SignalR connection state changed:', state);
        },
        onError: (error) => {
          console.error('SignalR error:', error);
        },
      };
      signalRService.setCallbacks(callbacks);

      // 并行加载初始数据和启动SignalR连接
      await Promise.all([
        loadNotifications(),
        loadUnreadCount(),
        signalRService.startConnection(),
      ]);

      initialized.value = true;
      console.log('Notification system initialized');
    } catch (error) {
      console.error('Failed to initialize notification system:', error);
      message.error('通知系统初始化失败');
    }
  }

  /**
   * 加载通知列表
   */
  async function loadNotifications(skip = 0, take = notificationConfig.ui.pageSize): Promise<void> {
    loading.value = true;
    try {
      const response = await notificationApi.getNotifications({
        ...filters.value,
        skip,
        take,
      });

      if (skip === 0) {
        notifications.value = response.items;
      } else {
        notifications.value.push(...response.items);
      }

      hasMore.value = response.hasMore;
    } catch (error) {
      console.error('Failed to load notifications:', error);
      message.error('加载通知失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 加载更多通知
   */
  async function loadMore(): Promise<void> {
    if (!hasMore.value || loading.value) return;
    await loadNotifications(notifications.value.length);
  }

  /**
   * 加载未读数量
   */
  async function loadUnreadCount(): Promise<void> {
    try {
      const count = await notificationApi.getUnreadCount();
      unreadCount.value = count;
    } catch (error) {
      console.error('Failed to load unread count:', error);
    }
  }

  /**
   * 刷新通知列表
   */
  async function refresh(): Promise<void> {
    await Promise.all([
      loadNotifications(),
      loadUnreadCount(),
    ]);
  }

  /**
   * 添加新通知（来自SignalR）
   */
  function addNotification(notification: Notification): void {
    // 去重检查
    const exists = notifications.value.find((n) => n.id === notification.id);
    if (!exists) {
      notifications.value.unshift(notification);
      
      // 限制通知数量
      if (notifications.value.length > notificationConfig.ui.maxNotifications) {
        notifications.value = notifications.value.slice(0, notificationConfig.ui.maxNotifications);
      }

      // 更新未读数量
      if (notification.status === NotificationStatus.Unread) {
        unreadCount.value++;
      }

      // 显示通知提示
      showNotificationToast(notification);
    }
  }

  /**
   * 标记通知为已读
   */
  async function markAsRead(id: string): Promise<void> {
    try {
      await notificationApi.markAsRead(id);
      
      const notification = notifications.value.find((n) => n.id === id);
      if (notification && notification.status === NotificationStatus.Unread) {
        notification.status = NotificationStatus.Read;
        notification.readAt = new Date().toISOString();
        unreadCount.value = Math.max(0, unreadCount.value - 1);
      }
    } catch (error) {
      console.error('Failed to mark as read:', error);
      message.error('标记已读失败');
    }
  }

  /**
   * 标记所有通知为已读
   */
  async function markAllAsRead(): Promise<void> {
    try {
      await notificationApi.markAllAsRead();
      
      notifications.value.forEach((n) => {
        if (n.status === NotificationStatus.Unread) {
          n.status = NotificationStatus.Read;
          n.readAt = new Date().toISOString();
        }
      });
      
      unreadCount.value = 0;
      message.success('已标记所有通知为已读');
    } catch (error) {
      console.error('Failed to mark all as read:', error);
      message.error('标记全部已读失败');
    }
  }

  /**
   * 删除通知
   */
  async function deleteNotification(id: string): Promise<void> {
    try {
      await notificationApi.deleteNotification(id);
      
      const index = notifications.value.findIndex((n) => n.id === id);
      if (index > -1) {
        const notification = notifications.value[index];
        notifications.value.splice(index, 1);
        
        if (notification.status === NotificationStatus.Unread) {
          unreadCount.value = Math.max(0, unreadCount.value - 1);
        }
      }
      
      message.success('通知已删除');
    } catch (error) {
      console.error('Failed to delete notification:', error);
      message.error('删除通知失败');
    }
  }

  /**
   * 批量操作通知
   */
  async function batchOperation(ids: string[], operation: 'read' | 'unread' | 'archive' | 'delete'): Promise<void> {
    try {
      const response = await notificationApi.batchOperation({ ids, operation });
      
      if (response.success) {
        // 根据操作类型更新本地状态
        switch (operation) {
          case 'read':
            notifications.value.forEach((n) => {
              if (ids.includes(n.id) && n.status === NotificationStatus.Unread) {
                n.status = NotificationStatus.Read;
                n.readAt = new Date().toISOString();
              }
            });
            await loadUnreadCount(); // 重新加载未读数量
            break;
          case 'delete':
            notifications.value = notifications.value.filter((n) => !ids.includes(n.id));
            await loadUnreadCount();
            break;
          case 'archive':
            notifications.value.forEach((n) => {
              if (ids.includes(n.id)) {
                n.status = NotificationStatus.Archived;
              }
            });
            await loadUnreadCount();
            break;
        }
        
        message.success(`批量${operation === 'read' ? '已读' : operation === 'delete' ? '删除' : '归档'}操作成功`);
      }
    } catch (error) {
      console.error('Batch operation failed:', error);
      message.error('批量操作失败');
    }
  }

  /**
   * 设置过滤器
   */
  function setFilters(newFilters: Partial<typeof filters.value>): void {
    filters.value = { ...filters.value, ...newFilters };
    loadNotifications(); // 重新加载
  }

  /**
   * 清空过滤器
   */
  function clearFilters(): void {
    filters.value = {};
    loadNotifications();
  }

  /**
   * 清空所有通知
   */
  async function clearAll(): Promise<void> {
    try {
      const allIds = notifications.value.map((n) => n.id);
      await batchOperation(allIds, 'delete');
      notifications.value = [];
      unreadCount.value = 0;
      message.success('已清空所有通知');
    } catch (error) {
      console.error('Failed to clear all notifications:', error);
      message.error('清空通知失败');
    }
  }

  /**
   * 处理新通知（SignalR回调）
   */
  function handleNewNotification(notification: Notification): void {
    addNotification(notification);
  }

  /**
   * 处理未读数量更新（SignalR回调）
   */
  function handleUnreadCountUpdate(count: number): void {
    unreadCount.value = count;
  }

  /**
   * 显示通知提示
   */
  function showNotificationToast(notification: Notification): void {
    // 根据通知类型显示不同的提示
    const config = {
      content: notification.content.title,
      description: notification.content.message,
      duration: 4.5,
    };

    switch (notification.type) {
      case NotificationType.Success:
        message.success(config);
        break;
      case NotificationType.Warning:
        message.warning(config);
        break;
      case NotificationType.Error:
        message.error(config);
        break;
      default:
        message.info(config);
        break;
    }
  }

  /**
   * 销毁通知系统
   */
  async function destroy(): Promise<void> {
    await signalRService.stopConnection();
    notifications.value = [];
    unreadCount.value = 0;
    initialized.value = false;
  }

  return {
    // 状态
    notifications,
    unreadCount,
    loading,
    hasMore,
    initialized,
    filters,
    
    // 计算属性
    filteredNotifications,
    unreadNotifications,
    notificationsByCategory,
    hasUnreadNotifications,
    
    // 方法
    initialize,
    loadNotifications,
    loadMore,
    loadUnreadCount,
    refresh,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    batchOperation,
    setFilters,
    clearFilters,
    clearAll,
    destroy,
  };
});

// 导出类型
export type NotificationStore = ReturnType<typeof useNotificationStore>;
