<script setup lang="ts">
import type { StabilityStudiesApi } from '#/api/stability-studies';

import { reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Modal as AModal, Button, message, Space } from 'ant-design-vue';

import { $deleteProtocolApi } from '#/api/business-static-tables/study-template';
import { $findNextStepOfWorkFlowApi } from '#/api/common';
import { downloadWitlabFile } from '#/api/core/witlab';
import {
  $addNewStudyApi,
  $assignZeroApi,
  $backStudyWfStepApi,
  $checkPreviousStepUserWFAccessApi,
  $closeStudyManagerApi,
  $executeStabStudyWFStepApi,
  $generateRRForStabApi,
  $getCompleteFileApi,
  $getDisplayParamsApi,
  $getPlanFlotExitApi,
  $getStudyFoldersApi,
  $getStudyTestsApi,
  $removedProtocolApi,
  $reOpenStudyApi,
  $stabilityStudiesDgApi,
  $stopStabilitysApi,
  $stopStudyApi,
  $validateBeforeStartStudyApi,
  $validateCompleteStudyApi,
  $validateStopStudyApi,
} from '#/api/stability-studies';
import { esignBegin } from '#/components/esig';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import SpecsForDynamic from '../business-static-tables/process-specifications/specs-for-dynamic.vue';
import Profiles from '../business-static-tables/sample-groups/profiles.vue';
import AddStabilityStudy from './add-stability-study.vue';
import AssignZeroInterval from './assign-zero-interval.vue';
import StabDetail from './components/stab-detail.vue';
import { useColumns } from './data';
import SelectSpecs from './select-specs.vue';
import StartStudyPartial from './start-study-partial.vue';

const route = useRoute();
const formVar = reactive<Record<string, any>>({}); // 模拟form.Variables变量
const mode = route.query.mode as string; // 页面模式
formVar.mode = mode;
formVar.bShowSelectPopup = false;
if (mode === 'StabilityReport') {
  formVar.WorkFlowCode = route.query.WorkFlowCode as string;
  formVar.StepCode = route.query.StepCode as string;
}
if (route.query.bShowSelectPopup) {
  const val = route.query.bShowSelectPopup;
  formVar.bShowSelectPopup = (Array.isArray(val) ? val[0] : val) === 'true';
}
const buttonConfigs = {
  AddStudyTemplate_lbtn: {
    visible: () => mode === 'Create',
  },
  Copy_lbtn: {
    visible: () => mode === 'Create',
  },
  Release_btn: {
    visible: () =>
      mode === 'Create' ||
      mode === 'Review Study' ||
      mode === 'Sampling Study' ||
      mode === 'Complete',
    enabled: () => {
      return (
        ((CurrentRow.value?.STEPCODE && CurrentRow.value.DISPSTEPCODE) ||
          CurrentRow.value?.STEPCODE === CurrentRow.value?.DISPSTEPCODE) &&
        (CurrentRow.value?.STEPCODE !== 'Start Study' ||
          CurrentRow.value?.STEPCODE !== 'Complete') &&
        CurrentRow.value?.ISSUBSTUDY !== 'Y'
      );
    },
  },
  StartPartial_lnkbtn: {
    visible: () => mode === 'LifeCycle',
  },
  Assign0_lnkbtn: {
    visible: () => mode === 'LifeCycle',
  },
  btnBackUp: {
    visible: () => mode === 'Review Study',
  },
  SetSpecs_lnkbtn: {
    visible: () => mode === 'Create' || mode === 'LifeCycle',
  },
  btnProfile: {
    visible: () =>
      mode === 'Create' ||
      mode === 'Review Study' ||
      mode === 'Edit' ||
      mode === 'View',
  },
  btnGenOrr: {
    visible: () =>
      mode === 'StabilitySumReport' ||
      mode === 'Edit' ||
      (mode === 'StabilityReport' && formVar.StepCode === 'Draft'),
    enabled: () =>
      CurrentRow.value?.FDISPSTAT &&
      CurrentRow.value.FDISPSTAT !== 'Stopped' &&
      CurrentRow.value.FDISPSTAT !== 'Removed',
  },
  btnViewORR: {
    visible: () =>
      mode === 'StabilitySumReport' ||
      mode === 'Edit' ||
      mode === 'View' ||
      (mode === 'StabilityReport' && formVar.StepCode === 'Draft'),
  },
  btnViewSpecs: {
    visible: () =>
      mode !== 'LifeCycle' &&
      mode !== 'Create' &&
      mode !== 'Review Study' &&
      mode !== 'Sampling Study',
  },
  Stop_btn: {
    visible: () => mode === 'Edit',
    enabled: () =>
      CurrentRow.value &&
      CurrentRow.value.FDISPSTAT === 'Started' &&
      CurrentRow.value.ISSUBSTUDY !== 'Y',
  },
  Delete_btn: {
    visible: () => mode === 'Create',
  },
  btnRemoved: {
    visible: () => mode === 'Edit',
    enabled: () => {
      return (
        CurrentRow.value &&
        CurrentRow.value.FDISPSTAT !== 'Completed' &&
        CurrentRow.value.FDISPSTAT !== 'Stopped' &&
        CurrentRow.value.FDISPSTAT !== 'Removed' &&
        CurrentRow.value.FDISPSTAT !== 'Started'
      );
    },
  },
};

const {
  Grid: StudyGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<StabilityStudiesApi.XFolder>(
  useColumns(mode),
  [],
  async (params) => {
    const { currentPage, pageSize } = params.page;
    return await $stabilityStudiesDgApi({
      params: {
        mode,
        filters: [
          null,
          [],
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ],
        stepCode: formVar.StepCode,
      },
      currentPage,
      pageSize,
    });
  },
  {
    params: {
      limsControlId: 'StabilityStudy_gd',
      tableName: 'XFOLDER',
    },
    rowConfig: {
      // useKey: true,
      keyField: 'ORIGREC', // 主键字段
      isCurrent: true, // 是否需要高亮当前行
    },
  },
  {
    // currentRowChange: () => {
    //   SetButtonDisplay();
    // },
  },
);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddStabilityStudy,
  destroyOnClose: true,
});

const [SpecModal, specModalApi] = useVbenModal({
  connectedComponent: SelectSpecs,
  destroyOnClose: true,
});
const [StartPartialModal, startPartialModalApi] = useVbenModal({
  connectedComponent: StartStudyPartial,
  destroyOnClose: true,
});
const [AssignZeroModal, assignZeroModalApi] = useVbenModal({
  connectedComponent: AssignZeroInterval,
  destroyOnClose: true,
});
const [ViewSpecModal, viewSpecModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: SpecsForDynamic,
});

const [Drawer, drawerApi] = useVbenDrawer({
  connectedComponent: StabDetail,
  destroyOnClose: true,
  zIndex: 1000,
});

function onCreate() {
  formModalApi.open();
}

function onSelectSpec() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  specModalApi
    .setData({
      nStabno: CurrentRow.value.STABNO,
      nSpcode: CurrentRow.value.SP_CODE,
      sOrigCurr: CurrentRow.value.SPECSTOUSE,
      nSpecO: CurrentRow.value.SPECIFICATION,
    })
    .open();
}

async function addSuccess(stabNo: number) {
  await gridApi.query();
  const data = gridApi.grid.getData().find((item) => item.STABNO === stabNo);
  if (data) {
    gridApi.grid.setCurrentRow(data);
  }
}

async function onDelete() {
  // 获取选中行
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }

  await confirm($t('commons.deleteConfirm'), $t('commons.question'));
  const stabNo = CurrentRow.value.STABNO;
  await $deleteProtocolApi({
    stabNo,
  });

  message.success($t('commons.deleteSuccess'));
  gridApi?.query();
}

async function onRemove() {
  // 获取选中行
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm(
    $t('stability-studies.ui.removeConfirm'),
    $t('commons.question'),
  );
  const stabNo = CurrentRow.value.STABNO;
  await $removedProtocolApi({
    stabNo,
  });
  gridApi?.query();
}

async function onCopy() {
  // 获取选中行
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const stabNo = CurrentRow.value.STABNO;
  // TODO: check previous step user workflow access 这个脚本会判定角色的权限，如果权限不足，会弹窗提示
  const bAccess = await $checkPreviousStepUserWFAccessApi({
    copyFromStabNo: stabNo,
  });
  if (!bAccess) {
    const res = await confirm(
      $t('commons.confirm'),
      $t('stability-studies.ui.noAccessToPreviousStep'),
    );
    if (!res) {
      return;
    }
  }
  const newStabNo = await $addNewStudyApi({
    protocolCode: stabNo,
    mode: 'COPY',
  });
  if (newStabNo && (mode === 'Create' || mode === 'Edit')) {
    await gridApi.query();
  }
}

async function validateSubmit() {
  if (!CurrentRow.value) {
    return false;
  }
  const StabNo = CurrentRow.value.STABNO;
  const StepCode = CurrentRow.value.STEPCODE;
  const Status = CurrentRow.value.FDISPSTAT;
  const sIsSubstudy = CurrentRow.value.ISSUBSTUDY;
  const aRet = await $getDisplayParamsApi({ StabNo, sIsSubstudy, Status });
  const bReadyToComplete = aRet[0];
  const bHasSamplesAssig = aRet[2];

  return (
    (Status === 'Completed' ||
      (StepCode === 'Complete' && bReadyToComplete && sIsSubstudy === 'N') ||
      (!(sIsSubstudy === 'Y') && Status !== 'Started' && bHasSamplesAssig)) &&
    mode !== 'View'
  );
}

async function onSubmit() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const valid = await validateSubmit();
  if (!valid) {
    message.warning($t('stability-studies.ui.submitError'));
    return;
  }
  const WorkFlowCode = CurrentRow.value.WORKFLOWCODE;
  let CurrStepCode = CurrentRow.value.DISPSTEPCODE;
  const StabNo = CurrentRow.value.STABNO;
  const SgroupOfStabs = CurrentRow.value.GROUPOFSTABS;
  let sStatus = CurrentRow.value.FDISPSTAT;
  const StepCode = CurrentRow.value.STEPCODE;
  const sSource = CurrentRow.value.SOURCE;
  // let sEventCode = '';
  let strMessage = '';
  switch (StepCode) {
    case 'Create': {
      for (const row of gridApi.grid.getData()) {
        if (row.GROUPOFSTABS === SgroupOfStabs) {
          if (!row.PLANNO) {
            message.warning($t('stability-studies.ui.planNoIsNull'));
            return;
          }
          if (!row.FLOTID) {
            message.warning($t('stability-studies.ui.flotIdIsNull'));
            return;
          }
          const bolExit = await $getPlanFlotExitApi({
            planNo: row.PLANNO,
            flotId: row.FLOTID,
          });
          if (bolExit > 1) {
            message.warning($t('stability-studies.ui.planFlotIsExit'));
            return;
          }
          if (!row.SPECSTOUSE) {
            message.warning($t('stability-studies.ui.specificationIsNull'));
            return;
          }
        }
      }
      // sEventCode = 'CommitStabilityStudy';
      strMessage = $t('stability-studies.ui.createOkOrNot');
      break;
    }
    case 'Review': {
      // sEventCode = 'ReleaseStabilityStudy';
      strMessage = $t('stability-studies.ui.reviewOkOrNot');
      break;
    }
    case 'Sampling Study': {
      if (!sSource) {
        message.warning($t('stability-studies.ui.sourceIsNull'));
        return;
      }
      // sEventCode = 'SamplingStabilityStudy';
      strMessage = $t('stability-studies.ui.samplingStudyOkOrNot');
      break;
    }
    case 'Complete': {
      // sEventCode = 'CompleteStabilityStudy';
      strMessage = $t('stability-studies.ui.completeOkOrNot');
      const bolExit = await $getCompleteFileApi({ stabNo: StabNo });
      if (!bolExit) {
        message.warning($t('stability-studies.ui.exitCompleteFile'));
      }
    }
  }

  const res = await confirm(strMessage, $t('commons.confirm'));
  if (!res) {
    return;
  }
  if (mode === 'Complete' && sStatus !== 'Completed') {
    const aStabInfo = await $validateCompleteStudyApi({ stabNo: StabNo });
    if (aStabInfo.length === 0) {
      return;
    }
    CurrStepCode = aStabInfo[0][0];
    sStatus = aStabInfo[0][1];
  }

  if (sStatus === 'Completed') {
    await ReOpenStudy(StabNo);
    return;
  }

  const StepParams = await $findNextStepOfWorkFlowApi({
    workFlowCode: WorkFlowCode,
    currStepCode: CurrStepCode,
  });
  const NextStepCode = StepParams[0];
  // const SignatureType = StepParams[1];
  // const GoToSteps = StepParams[2];
  try {
    // const oParams = { sParamsScript: 'GeneralWorkFlowActions.GetESigParams' };
    // const oESig = await ESIG_Begin(sEventCode);
    const oESig = {
      sComment: '',
      dtStartDate: '',
    };
    if (!oESig) {
      return;
    }
    if (!oESig.sComment) {
      oESig.sComment = 'N/A';
    }

    await $executeStabStudyWFStepApi({
      CurrStepCode,
      NextStepCode,
      StabNo,
      sComment: oESig.sComment,
      dtStartDate: oESig.dtStartDate,
      conditionList: ['ALL'],
      processGroup: 'Y',
    });
    await $closeStudyManagerApi({ mode });
    // refreshReminders();
    gridApi?.query();
  } finally {
    // ESIG_End();
  }
}

async function ReOpenStudy(nStabno: number) {
  try {
    // var oESig = await ESIG_Begin("ReOpen");
    // if(!oESig)
    // {
    // 	return;
    // }

    const bRet = await $reOpenStudyApi({ stabNo: nStabno, sComment: '' });

    if (bRet) {
      gridApi?.query();
    }
  } finally {
    // ESIG_End();
  }
}

const showDetailEvent = (row: any) => {
  drawerApi
    .setData({
      data: row,
      mode,
      formVar,
    })
    .open();
};

const openProfiles = ref(false);
const sEditMode = ref('ProtocolView');
const handleOkProfiles = () => {
  openProfiles.value = false;
};

const handleOpenProfiles = () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  sEditMode.value = 'ProtocolView';
  const status = CurrentRow.value.FDISPSTAT;
  if (status === 'Draft' && mode !== 'View') {
    sEditMode.value = 'ProtocolEdit';
  }
  if (mode === 'Edit') {
    sEditMode.value = 'ProtocolView';
    const stepCode = CurrentRow.value.STEPCODE;
    if (stepCode === 'Create' || stepCode === 'Review') {
      sEditMode.value = 'ProtocolEdit';
    }
  }
  if (status === 'Removed') {
    sEditMode.value = 'ProtocolView';
  }
  openProfiles.value = true;
};

const rejectStudy = async () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const groupOfStabs = CurrentRow.value.GROUPOFSTABS;
  const yes = await confirm(
    $t('commons.rejectConfirmTitle'),
    $t('commons.question'),
  );
  if (!yes) {
    return;
  }
  await $backStudyWfStepApi({ groupOfStabs });
  gridApi?.query();
};

const onStartPartial = async () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nStabno = CurrentRow.value.STABNO;
  const aValidate = await $validateBeforeStartStudyApi({ stabNo: nStabno });
  if (!aValidate[0]) {
    switch (aValidate[1]) {
      case 'MismatchSpecsButContinue':
      case 'noCurrentSpecForCurrentButContinue': {
        const sYesNo = await confirm($t(aValidate[1]), $t('commons.question'));
        if (!sYesNo) {
          return;
        }
        break;
      }
      case 'MismatchSpecsForOriginal':
      case 'noCurrentSpecForOriginal':
      case 'noLocationCode':
      case 'noSpecSet': {
        message.warning($t(`stability-studies.response.${aValidate[1]}`));
        return;
      }
    }
  }
  const nMainStabno = aValidate[2];
  startPartialModalApi
    .setData({
      stabInfo: CurrentRow.value,
      mainStabno: nMainStabno,
      mode,
    })
    .open();
};

const onAssignZero = async () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.pleaseCheckOneRow'));
    return;
  }
  const nStabno = CurrentRow.value.STABNO;
  const aRet = await $getStudyTestsApi({ stabNo: nStabno });
  if (aRet[0][0] === -90) {
    message.warning($t('stability-studies.response.noZeroInterval'));
    return;
  }
  if (aRet[0][0] === -91) {
    message.warning($t('stability-studies.response.noSamplesToPull'));
    return;
  }
  if (aRet[0][0] === -92) {
    message.warning($t('stability-studies.response.noSelTestsZeroInterval'));
    return;
  }
  const aStudyTest = aRet;
  assignZeroModalApi
    .setData({
      studyTest: aStudyTest,
      openingMode: 'ASSIGN',
      stabNo: nStabno,
      batchList: '',
      confirmCallback: async ({
        sBatchno,
        nBatchId,
        sFormPack,
        aSelection,
      }: {
        aSelection: any[];
        nBatchId: number;
        nStabno: number;
        sBatchno: string;
        sFormPack: string;
      }) => {
        const sRet = await $assignZeroApi({
          nBatchId,
          nStabno,
          sBatchno,
          sFormPack,
          aSelection,
        });
        switch (sRet) {
          case 'MISMATCH': {
            message.warning($t('stability-studies.response.MISMATCH'));
            return;
          }
          case 'noResults': {
            message.warning($t('stability-studies.response.noResults'));
            return;
          }
          case 'NOSPEC': {
            message.warning($t('stability-studies.response.NOSPEC'));
            return;
          }
          case 'S': {
            message.warning($t('stability-studies.response.noSamplesToPull'));
            return;
          }
        }
        gridApi.query();
      },
    })
    .open();
};

const handleGenOrr = async () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nStabno = CurrentRow.value.STABNO;
  const esignData = await esignBegin({
    eventCode: 'GenerateRR4Stability',
  });
  if (!esignData) {
    return;
  }
  const aRet = await $generateRRForStabApi({
    stabNo: nStabno,
  });
  if (!aRet[0]) {
    message.error(aRet[1]);
    return;
  }
  message.success($t('commons.success'));
  gridApi.query();
};

const handleViewOrr = async () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = CurrentRow.value.PATH_RAWRECORD_RPT;
  if (!docId) {
    message.warning($t('stability-studies.response.noRR'));
    return;
  }
  downloadWitlabFile(docId);
};

async function handleViewSpec() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nStabno = CurrentRow.value.STABNO;
  const folderNos = await $getStudyFoldersApi({ stabNo: nStabno });
  viewSpecModalApi
    .setData({
      folderNos,
    })
    .open();
}

async function handleStopStudy() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nStabno = CurrentRow.value.STABNO;
  const stopResean = CurrentRow.value.STOPRESEAN;
  if (!stopResean) {
    message.warning($t('stability-studies.response.noStopReason'));
    return;
  }
  // var aValidate = lims.CallServer("StudyManager.validateStopStudy", [nStabno]);
  const aValidate = await $validateStopStudyApi({ stabNo: nStabno });
  if (!aValidate[0]) {
    const yes = await confirm(
      $t('stability-studies.response.stoppedStudies', [aValidate[1]]),
      $t('commons.question'),
    );
    if (!yes) {
      return;
    }
  }
  const esignData = await esignBegin({
    eventCode: 'StopStabilityStudy',
  });
  if (!esignData) {
    return;
  }
  const bRet = await $stopStudyApi({
    stabNo: nStabno,
    comment: esignData.comment,
    stopStartedSubstudies: true,
  });
  // 	lims.CallServer ("StudyManager.CloseStudyManager", [ form.Variables["Mode"] ]);
  // lims.CallServer ("StudyManager.StopStabilitys", [ nStabno,nStopResean ]);
  await $closeStudyManagerApi({
    mode,
  });
  await $stopStabilitysApi({
    stabNo: nStabno,
    stopResean,
  });
  if (bRet) {
    gridApi.query();
  }
}
</script>

<template>
  <Page auto-content-height>
    <AModal
      v-model:open="openProfiles"
      @ok="handleOkProfiles"
      width="90%"
      :footer="null"
      :destroy-on-close="true"
    >
      <Profiles
        :draw-no="1"
        :sp-code="CurrentRow?.SP_CODE || 0"
        :opening-mode="sEditMode"
        class="h-4/5"
      />
    </AModal>
    <FormModal @success="addSuccess($event)" />
    <SpecModal @success="gridApi?.query()" />
    <StartPartialModal @success="gridApi?.query()" />
    <AssignZeroModal @success="gridApi?.query()" />
    <ViewSpecModal />
    <Drawer @refresh="gridApi?.query()" v-model:form-var="formVar" />
    <div class="flex h-full flex-col">
      <div class="h-full w-full">
        <StudyGrid>
          <template #toolbar-actions>
            <Space :size="[4, 0]" wrap>
              <Button
                type="primary"
                @click="onCreate"
                v-if="buttonConfigs.AddStudyTemplate_lbtn.visible()"
              >
                {{ $t('ui.actionTitle.create') }}
              </Button>
              <Button
                type="primary"
                danger
                @click="onDelete"
                v-if="buttonConfigs.Delete_btn.visible()"
              >
                {{ $t('ui.actionTitle.delete') }}
              </Button>
              <Button
                type="primary"
                danger
                @click="onRemove"
                v-if="buttonConfigs.btnRemoved.visible()"
                :disabled="!buttonConfigs.btnRemoved.enabled()"
              >
                {{ $t('stability-studies.ui.remove') }}
              </Button>

              <Button
                type="default"
                @click="onCopy"
                v-if="buttonConfigs.Copy_lbtn.visible()"
              >
                {{ $t('commons.copy') }}
              </Button>
              <Button
                type="default"
                @click="onSubmit"
                v-if="buttonConfigs.Release_btn.visible()"
              >
                {{ $t('commons.submit') }}
              </Button>
              <Button
                type="default"
                @click="onStartPartial"
                v-if="buttonConfigs.StartPartial_lnkbtn.visible()"
              >
                {{ $t('stability-studies.ui.start') }}
              </Button>
              <Button
                type="default"
                @click="onAssignZero"
                v-if="buttonConfigs.Assign0_lnkbtn.visible()"
              >
                {{ $t('stability-studies.ui.assignZero') }}
              </Button>
              <Button
                type="default"
                @click="rejectStudy"
                v-if="buttonConfigs.btnBackUp.visible()"
              >
                {{ $t('commons.reject') }}
              </Button>
              <Button
                type="default"
                @click="onSelectSpec"
                v-if="buttonConfigs.SetSpecs_lnkbtn.visible()"
              >
                {{ $t('stability-studies.ui.spec') }}
              </Button>
              <Button
                type="default"
                @click="handleOpenProfiles"
                v-if="buttonConfigs.btnProfile.visible()"
              >
                {{ $t('stability-studies.ui.profile') }}
              </Button>
              <Button
                @click="handleGenOrr"
                v-if="buttonConfigs.btnGenOrr.visible()"
                :disabled="!buttonConfigs.btnGenOrr.enabled()"
              >
                {{ $t('stability-studies.ui.genOrr') }}
              </Button>
              <Button
                @click="handleViewOrr"
                v-if="buttonConfigs.btnViewORR.visible()"
              >
                {{ $t('stability-studies.ui.viewOrr') }}
              </Button>
              <Button
                @click="handleViewSpec"
                v-if="buttonConfigs.btnViewSpecs.visible()"
              >
                {{ $t('stability-studies.ui.viewSpec') }}
              </Button>
              <Button
                @click="handleStopStudy"
                v-if="buttonConfigs.Stop_btn.visible()"
                :disabled="!buttonConfigs.Stop_btn.enabled()"
              >
                {{ $t('stability-studies.ui.stopStudy') }}
              </Button>
            </Space>
          </template>
          <template #action="{ row }">
            <template v-if="hasEditStatus(row)">
              <Button type="link" @click="saveRowEvent(row)">
                {{ $t('commons.save') }}
              </Button>
              <Button type="link" @click="cancelRowEvent(row)">
                {{ $t('commons.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button type="link" @click="editRowEvent(row)">
                {{ $t('commons.edit') }}
              </Button>
              <Button type="link" @click="showDetailEvent(row)">
                {{ $t('commons.detail') }}
              </Button>
            </template>
          </template>
        </StudyGrid>
      </div>
    </div>
  </Page>
</template>
