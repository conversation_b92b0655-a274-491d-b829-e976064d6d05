<template>
  <div class="notification-list">
    <!-- 空状态 -->
    <div v-if="notifications.length === 0 && !loading" class="notification-list__empty">
      <Empty
        description="暂无通知"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </div>

    <!-- 通知列表 -->
    <div v-else class="notification-list__content">
      <div
        v-for="notification in displayNotifications"
        :key="notification.id"
        class="notification-list__item"
      >
        <NotificationItem
          :notification="notification"
          :show-actions="showActions"
          :show-avatar="showAvatar"
          :show-time="showTime"
          :show-category="showCategory"
          @click="handleNotificationClick"
          @mark-read="handleMarkRead"
          @delete="handleDelete"
        />
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore && !loading" class="notification-list__load-more">
        <Button
          type="text"
          block
          @click="handleLoadMore"
        >
          加载更多
        </Button>
      </div>

      <!-- 加载中状态 -->
      <div v-if="loading" class="notification-list__loading">
        <div class="flex items-center justify-center py-4">
          <Spin size="small" />
          <span class="ml-2 text-sm text-gray-500">加载中...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Notification } from '#/types/notification';
import type { NotificationListEvents, NotificationListProps } from './types';

import { computed } from 'vue';

import { Button, Empty, Spin } from 'ant-design-vue';

import NotificationItem from './notification-item.vue';

// Props定义
const props = withDefaults(defineProps<NotificationListProps>(), {
  loading: false,
  hasMore: false,
  showActions: true,
  showAvatar: true,
  showTime: true,
  showCategory: true,
  maxItems: 50,
});

// 事件定义
const emit = defineEmits<NotificationListEvents>();

// 计算属性
const displayNotifications = computed(() => {
  if (props.maxItems && props.notifications.length > props.maxItems) {
    return props.notifications.slice(0, props.maxItems);
  }
  return props.notifications;
});

// 事件处理
function handleNotificationClick(notification: Notification) {
  emit('notification-click', notification);
}

function handleMarkRead(id: string) {
  emit('mark-read', id);
}

function handleDelete(id: string) {
  emit('delete', id);
}

function handleLoadMore() {
  emit('load-more');
}
</script>

<style scoped>
.notification-list {
  @apply w-full;
}

.notification-list__empty {
  @apply py-8;
}

.notification-list__content {
  @apply w-full;
}

.notification-list__item {
  @apply border-b border-gray-100 last:border-b-0;
}

.notification-list__load-more {
  @apply p-2 border-t border-gray-100;
}

.notification-list__loading {
  @apply border-t border-gray-100;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-list__item {
    @apply border-b border-gray-200;
  }
}
</style>
