/**
 * 消息通知模块配置
 * 集成SignalR实时通信和API配置
 */

import type { NotificationConfig } from '#/types/notification';

/**
 * 通知模块配置
 */
export const notificationConfig: NotificationConfig = {
  // SignalR配置
  signalR: {
    hubUrl: import.meta.env.VITE_SIGNALR_HUB_URL || '/notificationHub',
    reconnectDelay: 5000, // 5秒重连延迟
    maxReconnectAttempts: 5, // 最大重连次数
  },

  // API配置
  api: {
    baseUrl: import.meta.env.VITE_WITLAB_API_URL || '/api',
    endpoints: {
      notifications: '/notifications',
      templates: '/notifications/templates',
      unreadCount: '/notifications/unread-count',
    },
  },

  // 缓存配置
  cache: {
    ttl: 300_000, // 5分钟缓存时间
    maxSize: 100, // 最大缓存条目数
  },

  // UI配置
  ui: {
    pageSize: 20, // 分页大小
    maxNotifications: 100, // 最大通知数量
    autoMarkReadDelay: 3000, // 自动标记已读延迟（毫秒）
  },
};

/**
 * 通知类型配置
 */
export const notificationTypeConfig = {
  0: {
    name: '信息',
    color: '#1890ff',
    icon: 'Info',
  },
  1: {
    name: '警告',
    color: '#faad14',
    icon: 'AlertTriangle',
  },
  2: {
    name: '错误',
    color: '#ff4d4f',
    icon: 'XCircle',
  },
  3: {
    name: '成功',
    color: '#52c41a',
    icon: 'CheckCircle',
  },
} as const;

/**
 * 通知分类配置
 */
export const notificationCategoryConfig = {
  0: {
    name: '系统',
    color: '#722ed1',
    description: '系统相关通知',
  },
  1: {
    name: '业务',
    color: '#13c2c2',
    description: '业务流程通知',
  },
  2: {
    name: '安全',
    color: '#f5222d',
    description: '安全相关通知',
  },
  3: {
    name: '个人',
    color: '#52c41a',
    description: '个人相关通知',
  },
} as const;

/**
 * 通知优先级配置
 */
export const notificationPriorityConfig = {
  0: {
    name: '低',
    color: '#d9d9d9',
    weight: 1,
  },
  1: {
    name: '普通',
    color: '#1890ff',
    weight: 2,
  },
  2: {
    name: '高',
    color: '#faad14',
    weight: 3,
  },
  3: {
    name: '紧急',
    color: '#ff4d4f',
    weight: 4,
  },
} as const;

/**
 * 通知状态配置
 */
export const notificationStatusConfig = {
  0: {
    name: '未读',
    color: '#ff4d4f',
    badge: true,
  },
  1: {
    name: '已读',
    color: '#d9d9d9',
    badge: false,
  },
  2: {
    name: '已归档',
    color: '#8c8c8c',
    badge: false,
  },
} as const;

/**
 * 默认头像配置
 */
export const defaultAvatarConfig = {
  system: 'https://avatar.vercel.sh/system.svg?text=SYS',
  business: 'https://avatar.vercel.sh/business.svg?text=BIZ',
  security: 'https://avatar.vercel.sh/security.svg?text=SEC',
  personal: 'https://avatar.vercel.sh/personal.svg?text=PER',
  default: 'https://avatar.vercel.sh/notification.svg?text=N',
};

/**
 * 时间格式化配置
 */
export const timeFormatConfig = {
  // 相对时间阈值（毫秒）
  justNow: 60_000, // 1分钟内显示"刚刚"
  minutes: 3_600_000, // 1小时内显示"X分钟前"
  hours: 86_400_000, // 1天内显示"X小时前"
  days: 604_800_000, // 1周内显示"X天前"
  // 超过1周显示具体日期
};

/**
 * 动画配置
 */
export const animationConfig = {
  // 通知项动画
  notification: {
    enter: 'animate-slide-in-right',
    leave: 'animate-slide-out-right',
    duration: 300,
  },
  // 加载动画
  loading: {
    spinner: 'animate-spin',
    pulse: 'animate-pulse',
  },
  // 铃铛摇摆动画
  bell: {
    ring: 'animate-bell-ring',
    duration: 1000,
  },
};

/**
 * 响应式断点配置
 */
export const breakpointConfig = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
};

/**
 * 获取通知类型配置
 */
export function getNotificationTypeConfig(type: number) {
  return notificationTypeConfig[type as keyof typeof notificationTypeConfig] || notificationTypeConfig[0];
}

/**
 * 获取通知分类配置
 */
export function getNotificationCategoryConfig(category: number) {
  return notificationCategoryConfig[category as keyof typeof notificationCategoryConfig] || notificationCategoryConfig[0];
}

/**
 * 获取通知优先级配置
 */
export function getNotificationPriorityConfig(priority: number) {
  return notificationPriorityConfig[priority as keyof typeof notificationPriorityConfig] || notificationPriorityConfig[1];
}

/**
 * 获取通知状态配置
 */
export function getNotificationStatusConfig(status: number) {
  return notificationStatusConfig[status as keyof typeof notificationStatusConfig] || notificationStatusConfig[0];
}

/**
 * 获取默认头像
 */
export function getDefaultAvatar(category?: number): string {
  switch (category) {
    case 0:
      return defaultAvatarConfig.system;
    case 1:
      return defaultAvatarConfig.business;
    case 2:
      return defaultAvatarConfig.security;
    case 3:
      return defaultAvatarConfig.personal;
    default:
      return defaultAvatarConfig.default;
  }
}

/**
 * 环境变量验证
 */
export function validateNotificationConfig(): boolean {
  const requiredEnvVars = [
    'VITE_SIGNALR_HUB_URL',
    'VITE_API_BASE_URL',
  ];

  const missingVars = requiredEnvVars.filter(
    (varName) => !import.meta.env[varName],
  );

  if (missingVars.length > 0) {
    console.warn(
      `Missing notification environment variables: ${missingVars.join(', ')}`,
    );
    return false;
  }

  return true;
}
