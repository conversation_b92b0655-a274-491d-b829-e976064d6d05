<script lang="ts" setup>
import type {
  UploadChangeParam,
  UploadFile,
  UploadProps,
} from 'ant-design-vue';

import type { CommonAppsApi } from '#/api/common-apps/eln-running';

import { ref, watch } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, Upload } from 'ant-design-vue';

import {
  addAttachmentApi,
  deleteAttachmentApi,
  dgAttachmentsApi,
} from '#/api/common-apps/eln-running';
import {
  downloadWitlabFile,
  getServerTempFilePathByWitlabId,
  UploadWitlabTempFile,
} from '#/api/core/witlab';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useAttachmentChromColumns,
  useAttachmentChromFilterSchema,
} from './eln-running-data';

// const emit = defineEmits(['success']);

const sTable = ref<string>('');
const sId = ref<number>(0);
const sReadonly = ref<boolean>(false);

const fileList = ref<UploadProps['fileList']>([]); // 文件列表
const headers = {
  authorization: 'authorization-text',
};

const colums = useAttachmentChromColumns();
const filterSchema = useAttachmentChromFilterSchema();
const queryData = async () => {
  if (sReadonly.value && sTable.value === 'RESULTS') {
    sTable.value = 'ALL';
  }
  const data = await dgAttachmentsApi(sTable.value, sId.value);
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid, gridApi, CurrentRow } =
  useLimsGridsConfig<CommonAppsApi.RunAttachments>(
    colums,
    filterSchema,
    queryData,
    girdOption,
  );

function onRefresh() {
  gridApi.query();
}

const isDisabled = ref(false);

watch(
  () => sReadonly.value,
  async (_val) => {
    if (_val) {
      isDisabled.value = true;
    }
  },
);

const uploadDocuments = async (info: UploadChangeParam) => {
  if (
    info.file &&
    info.fileList &&
    info.fileList[0] &&
    info.fileList[0].originFileObj
  ) {
    gridApi.setLoading(true);
    const tempPath = await UploadWitlabTempFile(info.fileList[0].originFileObj);
    const tempServerPath = await getServerTempFilePathByWitlabId({
      key: tempPath.AccessKey,
      fileId: tempPath.FileId,
    });
    if (!tempServerPath) {
      message.error($t('commons.uploadFailed'));
      return;
    }
    await addAttachmentApi(
      info.file.name,
      tempServerPath,
      sTable.value,
      sId.value,
    );
    gridApi.query();
    gridApi.setLoading(false);
  }
};

const beforeUpload = (file: UploadFile) => {
  fileList.value = [...(fileList.value || []), file];
  return false;
};

const viewDocuments = async function () {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = CurrentRow.value.STARDOC_ID;
  const fileName = CurrentRow.value.ATTACHMENT;
  downloadWitlabFile(docId, fileName);
};

// 删除
async function onDelete() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) return;

  const aStardocId: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.STARDOC_ID);

  try {
    await confirm({
      title: '删除',
      content: `确定要删除选中的附件吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteAttachmentApi(aOrigrec, aStardocId);

    message.success('操作成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any>>();
      if (data) {
        sTable.value = data.TABLE;
        sId.value = data.SID;
        sReadonly.value = data.READONLY;
        onRefresh();
      }
    }
  },
  title: '附件谱图',
});
</script>

<template>
  <Modal class="h-[700px] w-[1200px]">
    <div class="flex h-full flex-col">
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Upload
              v-model:file-list="fileList"
              name="file"
              :show-upload-list="false"
              :headers="headers"
              :max-count="1"
              @change="uploadDocuments"
              :before-upload="beforeUpload"
            >
              <Button type="primary" :disabled="isDisabled">
                {{ $t('common-apps.elnRunning.upload') }}
              </Button>
            </Upload>
            <Button
              type="primary"
              danger
              @click="onDelete"
              :disabled="isDisabled"
            >
              {{ $t('common-apps.elnRunning.delete') }}
            </Button>
            <Button type="default" @click="viewDocuments">
              {{ $t('common-apps.elnRunning.view') }}
            </Button>
            <Button type="default" :disabled="isDisabled">
              {{ $t('common-apps.elnRunning.photo') }}
            </Button>
          </Space>
        </template>
      </Grid>
    </div>
  </Modal>
</template>
