● Vue3 消息通知模块开发指导手册

  1. 项目集成准备

  1.1 依赖安装

  npm install @microsoft/signalr axios pinia
  npm install -D @types/signalr

  1.2 环境配置

  // src/config/notification.ts
  export const notificationConfig = {
    signalR: {
      hubUrl: import.meta.env.VITE_SIGNALR_HUB_URL || '/notificationHub',
      reconnectDelay: 5000,
      maxReconnectAttempts: 5
    },
    api: {
      baseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
      endpoints: {
        notifications: '/notifications',
        templates: '/notifications/templates',
        unreadCount: '/notifications/unread-count'
      }
    },
    cache: {
      ttl: 300000, // 5分钟
      maxSize: 100
    }
  }

  2. 核心服务封装

  2.1 SignalR服务

  // src/services/signalRService.ts
  import * as signalR from '@microsoft/signalr'
  import { notificationConfig } from '@/config/notification'
  import { useUserStore } from '@/stores/user'

  class SignalRService {
    private connection: signalR.HubConnection | null = null
    private reconnectTimer: number = 0
    private isReconnecting = false

    async startConnection() {
      const userStore = useUserStore()

      this.connection = new signalR.HubConnectionBuilder()
        .withUrl(notificationConfig.signalR.hubUrl, {
          accessTokenFactory: () => userStore.token || ''
        })
        .withAutomaticReconnect({
          nextRetryDelayInMilliseconds: () => notificationConfig.signalR.reconnectDelay
        })
        .build()

      this.setupEventHandlers()

      try {
        await this.connection.start()
        console.log('SignalR Connected')
      } catch (error) {
        console.error('SignalR Connection Error:', error)
        this.scheduleReconnect()
      }
    }

    private setupEventHandlers() {
      if (!this.connection) return

      // 接收新通知
      this.connection.on('ReceiveNotification', (notification: Notification) => {
        const notificationStore = useNotificationStore()
        notificationStore.addNotification(notification)
      })

      // 未读数量更新
      this.connection.on('UnreadCountUpdate', (data: { userId: string; unreadCount: number }) => {
        const notificationStore = useNotificationStore()
        notificationStore.setUnreadCount(data.unreadCount)
      })

      // 连接状态管理
      this.connection.onreconnecting(() => {
        console.log('SignalR Reconnecting...')
      })

      this.connection.onreconnected(() => {
        console.log('SignalR Reconnected')
      })
    }

    async joinRoleGroup(roleName: string) {
      if (this.connection?.state === signalR.HubConnectionState.Connected) {
        await this.connection.invoke('JoinRoleGroup', roleName)
      }
    }

    async leaveRoleGroup(roleName: string) {
      if (this.connection?.state === signalR.HubConnectionState.Connected) {
        await this.connection.invoke('LeaveRoleGroup', roleName)
      }
    }

    private scheduleReconnect() {
      if (this.isReconnecting) return

      this.isReconnecting = true
      this.reconnectTimer = window.setTimeout(() => {
        this.startConnection()
        this.isReconnecting = false
      }, notificationConfig.signalR.reconnectDelay)
    }

    async stopConnection() {
      if (this.connection) {
        await this.connection.stop()
      }
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
      }
    }
  }

  export const signalRService = new SignalRService()

  2.2 HTTP API服务

  // src/services/notificationApi.ts
  import axios from '@/utils/axios'
  import type { Notification, NotificationTemplate } from '@/types/notification'

  export class NotificationApiService {
    // 获取用户通知列表
    async getNotifications(params: {
      status?: number
      category?: number
      skip?: number
      take?: number
    } = {}) {
      const response = await axios.get('/notifications', { params })
      return response.data
    }

    // 获取未读通知数量
    async getUnreadCount() {
      const response = await axios.get('/notifications/unread-count')
      return response.data
    }

    // 获取通知详情
    async getNotificationById(id: string) {
      const response = await axios.get(`/notifications/${id}`)
      return response.data
    }

    // 创建通知
    async createNotification(data: CreateNotificationRequest) {
      const response = await axios.post('/notifications', data)
      return response.data
    }

    // 标记通知已读
    async markAsRead(id: string) {
      const response = await axios.put(`/notifications/${id}/read`)
      return response.data
    }

    // 标记所有通知已读
    async markAllAsRead() {
      const response = await axios.put('/notifications/read-all')
      return response.data
    }

    // 获取通知模板列表
    async getTemplates(params?: {
      isActive?: boolean
      category?: number
      skip?: number
      take?: number
    }) {
      const response = await axios.get('/notifications/templates', { params })
      return response.data
    }

    // 通过模板创建通知
    async createFromTemplate(data: {
      templateCode: string
      userId?: string
      parameters?: Record<string, string>
    }) {
      const response = await axios.post('/notifications/from-template', data)
      return response.data
    }
  }

  export const notificationApi = new NotificationApiService()

  2.3 Pinia状态管理

  // src/stores/notification.ts
  import { defineStore } from 'pinia'
  import { notificationApi } from '@/services/notificationApi'
  import { signalRService } from '@/services/signalRService'
  import type { Notification, NotificationState } from '@/types/notification'

  export const useNotificationStore = defineStore('notification', {
    state: (): NotificationState => ({
      notifications: [],
      unreadCount: 0,
      loading: false,
      hasMore: true,
      filters: {
        status: undefined,
        category: undefined
      }
    }),

    getters: {
      filteredNotifications: (state) => {
        return state.notifications.filter(notification => {
          if (state.filters.status !== undefined && notification.status !== state.filters.status) {
            return false
          }
          if (state.filters.category !== undefined && notification.category !== state.filters.category) {
            return false
          }
          return true
        })
      },

      unreadNotifications: (state) =>
        state.notifications.filter(n => n.status === 0),

      notificationsByCategory: (state) =>
        state.notifications.reduce((acc, notification) => {
          const category = notification.category
          if (!acc[category]) acc[category] = []
          acc[category].push(notification)
          return acc
        }, {} as Record<number, Notification[]>)
    },

    actions: {
      async initialize() {
        await Promise.all([
          this.loadNotifications(),
          this.loadUnreadCount()
        ])
        await signalRService.startConnection()
      },

      async loadNotifications(skip = 0, take = 20) {
        this.loading = true
        try {
          const response = await notificationApi.getNotifications({
            ...this.filters,
            skip,
            take
          })

          if (skip === 0) {
            this.notifications = response.items
          } else {
            this.notifications.push(...response.items)
          }

          this.hasMore = response.items.length === take
        } finally {
          this.loading = false
        }
      },

      async loadMore() {
        if (!this.hasMore || this.loading) return
        await this.loadNotifications(this.notifications.length)
      },

      async loadUnreadCount() {
        try {
          const response = await notificationApi.getUnreadCount()
          this.unreadCount = response.unreadCount
        } catch (error) {
          console.error('Failed to load unread count:', error)
        }
      },

      addNotification(notification: Notification) {
        // 去重检查
        const exists = this.notifications.find(n => n.id === notification.id)
        if (!exists) {
          this.notifications.unshift(notification)
          if (notification.status === 0) {
            this.unreadCount++
          }
        }
      },

      async markAsRead(id: string) {
        try {
          await notificationApi.markAsRead(id)
          const notification = this.notifications.find(n => n.id === id)
          if (notification && notification.status === 0) {
            notification.status = 1
            this.unreadCount = Math.max(0, this.unreadCount - 1)
          }
        } catch (error) {
          console.error('Failed to mark as read:', error)
        }
      },

      async markAllAsRead() {
        try {
          await notificationApi.markAllAsRead()
          this.notifications.forEach(n => {
            if (n.status === 0) n.status = 1
          })
          this.unreadCount = 0
        } catch (error) {
          console.error('Failed to mark all as read:', error)
        }
      },

      setFilters(filters: Partial<NotificationState['filters']>) {
        this.filters = { ...this.filters, ...filters }
        this.loadNotifications() // 重新加载
      },

      clearFilters() {
        this.filters = { status: undefined, category: undefined }
        this.loadNotifications()
      }
    }
  })

  3. 组件开发

  3.1 通知中心组件

  <!-- src/components/NotificationCenter.vue -->
  <template>
    <div class="notification-center">
      <!-- 触发按钮 -->
      <el-badge :value="unreadCount" :max="99" class="notification-badge">
        <el-button
          circle
          @click="togglePanel"
          :type="unreadCount > 0 ? 'danger' : 'default'"
        >
          <el-icon><Bell /></el-icon>
        </el-button>
      </el-badge>

      <!-- 通知面板 -->
      <el-popover
        v-model:visible="panelVisible"
        placement="bottom-end"
        :width="400"
        trigger="click"
      >
        <template #reference>
          <div></div>
        </template>

        <div class="notification-panel">
          <div class="panel-header">
            <h4>消息通知</h4>
            <div class="panel-actions">
              <el-link
                type="primary"
                :underline="false"
                @click="markAllAsRead"
                v-if="unreadCount > 0"
              >
                全部已读
              </el-link>
            </div>
          </div>

          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部" name="all">
              <NotificationList
                :notifications="filteredNotifications"
                :loading="loading"
                @load-more="loadMore"
                @mark-read="markAsRead"
              />
            </el-tab-pane>
            <el-tab-pane label="未读" name="unread">
              <NotificationList
                :notifications="unreadNotifications"
                :loading="loading"
                @load-more="loadMore"
                @mark-read="markAsRead"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-popover>
    </div>
  </template>

  <script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import { Bell } from '@element-plus/icons-vue'
  import { useNotificationStore } from '@/stores/notification'
  import NotificationList from './NotificationList.vue'

  const notificationStore = useNotificationStore()
  const panelVisible = ref(false)
  const activeTab = ref('all')

  const unreadCount = computed(() => notificationStore.unreadCount)
  const filteredNotifications = computed(() => notificationStore.filteredNotifications)
  const unreadNotifications = computed(() => notificationStore.unreadNotifications)
  const loading = computed(() => notificationStore.loading)

  const togglePanel = () => {
    panelVisible.value = !panelVisible.value
    if (panelVisible.value) {
      notificationStore.loadNotifications()
    }
  }

  const markAllAsRead = async () => {
    await notificationStore.markAllAsRead()
  }

  const loadMore = () => {
    notificationStore.loadMore()
  }

  const markAsRead = async (id: string) => {
    await notificationStore.markAsRead(id)
  }

  const handleTabChange = () => {
    // 可以在这里更新过滤器
  }

  onMounted(() => {
    notificationStore.initialize()
  })

  onUnmounted(() => {
    // 清理连接
  })
  </script>

  3.2 通知列表组件

  <!-- src/components/NotificationList.vue -->
  <template>
    <div class="notification-list">
      <el-scrollbar height="400px">
        <div v-if="notifications.length === 0" class="empty-state">
          <el-empty description="暂无通知" />
        </div>

        <div v-else>
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ unread: notification.status === 0 }"
            @click="handleClick(notification)"
          >
            <div class="notification-icon">
              <el-icon :style="{ color: getIconColor(notification.type) }">
                <component :is="getIconComponent(notification.type)" />
              </el-icon>
            </div>

            <div class="notification-content">
              <div class="notification-title">{{ notification.content?.title }}</div>
              <div class="notification-message">{{ notification.content?.message }}</div>
              <div class="notification-meta">
                <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
                <el-tag size="small" :type="getCategoryType(notification.category)">
                  {{ getCategoryName(notification.category) }}
                </el-tag>
              </div>
            </div>

            <div class="notification-actions">
              <el-button
                v-if="notification.status === 0"
                size="small"
                text
                @click.stop="handleMarkRead(notification.id)"
              >
                标为已读
              </el-button>
            </div>
          </div>

          <div v-if="loading" class="loading-more">
            <el-loading />
          </div>

          <div v-if="hasMore && !loading" class="load-more">
            <el-button text @click="$emit('load-more')">
              加载更多
            </el-button>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </template>

  <script setup lang="ts">
  import { computed } from 'vue'
  import type { Notification } from '@/types/notification'
  import { ElMessage } from 'element-plus'

  interface Props {
    notifications: Notification[]
    loading?: boolean
    hasMore?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    hasMore: false
  })

  const emit = defineEmits<{
    markRead: [id: string]
    loadMore: []
    click: [notification: Notification]
  }>()

  const getIconComponent = (type: number) => {
    const icons = {
      0: 'InfoFilled',    // Info
      1: 'WarningFilled', // Warning
      2: 'CircleCloseFilled', // Error
      3: 'SuccessFilled'  // Success
    }
    return icons[type] || 'InfoFilled'
  }

  const getIconColor = (type: number) => {
    const colors = {
      0: '#909399', // Info
      1: '#E6A23C', // Warning
      2: '#F56C6C', // Error
      3: '#67C23A'  // Success
    }
    return colors[type] || '#909399'
  }

  const getCategoryType = (category: number) => {
    const types = {
      0: 'info',    // System
      1: '',        // Business
      2: 'danger',  // Security
      3: 'success'  // Personal
    }
    return types[category] || 'info'
  }

  const getCategoryName = (category: number) => {
    const names = {
      0: '系统',
      1: '业务',
      2: '安全',
      3: '个人'
    }
    return names[category] || '其他'
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
    if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

    return date.toLocaleDateString()
  }

  const handleClick = (notification: Notification) => {
    emit('click', notification)

    if (notification.status === 0) {
      emit('markRead', notification.id)
    }
  }

  const handleMarkRead = async (id: string) => {
    emit('markRead', id)
  }
  </script>

  4. 类型定义

  // src/types/notification.ts
  export interface Notification {
    id: string
    content: {
      title: string
      message: string
      details?: string
      data?: string
    }
    type: NotificationType
    category: NotificationCategory
    priority: NotificationPriority
    status: NotificationStatus
    recipients: string[]
    expiresAt?: string
    sourceType?: string
    sourceId?: string
    createdAt: string
    readAt?: string
  }

  export interface NotificationTemplate {
    id: string
    code: string
    name: string
    title: string
    content: string
    type: NotificationType
    category: NotificationCategory
    priority: NotificationPriority
    isActive: boolean
    variables: TemplateVariable[]
    createdAt: string
  }

  export interface TemplateVariable {
    name: string
    displayName: string
    isRequired: boolean
    defaultValue?: string
  }

  export interface NotificationState {
    notifications: Notification[]
    unreadCount: number
    loading: boolean
    hasMore: boolean
    filters: {
      status?: number
      category?: number
    }
  }

  // 枚举定义
  export enum NotificationType {
    Info = 0,
    Warning = 1,
    Error = 2,
    Success = 3
  }

  export enum NotificationCategory {
    System = 0,
    Business = 1,
    Security = 2,
    Personal = 3
  }

  export enum NotificationPriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
  }

  export enum NotificationStatus {
    Unread = 0,
    Read = 1,
    Archived = 2
  }

  // API请求类型
  export interface CreateNotificationRequest {
    userId?: string
    title: string
    content: string
    type: NotificationType
    category: NotificationCategory
    priority?: NotificationPriority
    sourceType?: string
    sourceId?: string
    data?: string
    expiresAt?: string
  }

  export interface CreateNotificationFromTemplateRequest {
    templateCode: string
    userId?: string
    parameters?: Record<string, string>
  }

  5. 使用示例

  5.1 在应用中使用通知中心

  <!-- src/App.vue -->
  <template>
    <div id="app">
      <router-view />
      <NotificationCenter />
    </div>
  </template>

  <script setup lang="ts">
  import NotificationCenter from '@/components/NotificationCenter.vue'
  </script>

  5.2 发送通知

  // 在组件中使用
  import { notificationApi } from '@/services/notificationApi'

  // 发送简单通知
  await notificationApi.createNotification({
    title: '系统更新',
    content: '系统将在今晚10点进行维护',
    type: NotificationType.Info,
    category: NotificationCategory.System,
    priority: NotificationPriority.Normal
  })

  // 使用模板发送
  await notificationApi.createFromTemplate({
    templateCode: 'order-completed',
    userId: 'user-123',
    parameters: {
      orderNumber: '2024001',
      amount: '99.99'
    }
  })

  5.3 监听通知事件

  // 在组件中监听
  import { signalRService } from '@/services/signalRService'

  // 加入角色组（管理员接收所有通知）
  await signalRService.joinRoleGroup('Admin')

  // 离开角色组
  await signalRService.leaveRoleGroup('Admin')

  6. 样式定制

  // src/styles/notification.scss
  .notification-center {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
  }

  .notification-panel {
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .notification-item {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }

    &.unread {
      background-color: #f0f9ff;
    }

    .notification-icon {
      margin-right: 12px;
      margin-top: 2px;
    }

    .notification-content {
      flex: 1;

      .notification-title {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .notification-message {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .notification-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .notification-time {
          color: #999;
          font-size: 12px;
        }
      }
    }

    .notification-actions {
      margin-left: 12px;
    }
  }

  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }

  7. 错误处理

  // src/utils/notificationErrorHandler.ts
  export class NotificationErrorHandler {
    static handleConnectionError(error: Error) {
      console.error('Notification connection error:', error)
      ElMessage.error('通知服务连接失败，请刷新页面重试')
    }

    static handleApiError(error: any) {
      const message = error.response?.data?.message || '操作失败'
      ElMessage.error(message)
    }
  }

  8. 开发调试

  // src/debug/notificationDebug.ts
  export const notificationDebug = {
    // 模拟通知
    mockNotification(): Notification {
      return {
        id: 'mock-' + Date.now(),
        content: {
          title: '测试通知',
          message: '这是一条测试通知内容',
          data: JSON.stringify({ test: true })
        },
        type: NotificationType.Info,
        category: NotificationCategory.System,
        priority: NotificationPriority.Normal,
        status: NotificationStatus.Unread,
        recipients: [],
        createdAt: new Date().toISOString()
      }
    },

    // 测试SignalR连接
    async testConnection() {
      try {
        await signalRService.startConnection()
        console.log('SignalR连接成功')
      } catch (error) {
        console.error('SignalR连接失败:', error)
      }
    }
  }

  这个完整的开发指导手册提供了Vue3消息通知模块的所有必要组件，包括：
  - 完整的SignalR集成
  - 状态管理
  - 组件开发
  - API对接
  - 样式定制
  - 错误处理

  可以直接用于生产环境开发。