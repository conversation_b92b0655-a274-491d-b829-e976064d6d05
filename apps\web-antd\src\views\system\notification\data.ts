/**
 * 通知管理页面数据配置
 * 参考现有系统页面的设计模式
 */

import type { VxeGridPropTypes } from 'vxe-table';

import type { Notification, NotificationTemplate } from '#/types/notification';

import { h } from 'vue';

import { Badge, Button, Space, Tag } from 'ant-design-vue';
import { CheckCircle, Eye, MoreHorizontal, Trash2 } from 'lucide-vue-next';

import {
  getNotificationCategoryConfig,
  getNotificationPriorityConfig,
  getNotificationStatusConfig,
  getNotificationTypeConfig,
} from '#/config/notification';
import { $t } from '#/locales';

/**
 * 通知列表表格列配置
 */
export function useNotificationColumns(
  onActionClick: (params: any) => void,
  onStatusChange?: (newStatus: number, row: Notification) => Promise<boolean>,
): VxeGridPropTypes.Columns {
  return [
    {
      type: 'checkbox',
      width: 50,
      fixed: 'left',
    },
    {
      field: 'content.title',
      title: '标题',
      minWidth: 200,
      showOverflow: 'tooltip',
      slots: {
        default: ({ row }: { row: Notification }) => {
          const statusConfig = getNotificationStatusConfig(row.status);
          return h('div', { class: 'flex items-center gap-2' }, [
            row.status === 0 && h('div', { class: 'w-2 h-2 bg-red-500 rounded-full' }),
            h('span', { class: row.status === 0 ? 'font-medium' : '' }, row.content.title),
          ]);
        },
      },
    },
    {
      field: 'content.message',
      title: '内容',
      minWidth: 250,
      showOverflow: 'tooltip',
    },
    {
      field: 'type',
      title: '类型',
      width: 100,
      slots: {
        default: ({ row }: { row: Notification }) => {
          const typeConfig = getNotificationTypeConfig(row.type);
          return h(Tag, { color: typeConfig.color }, () => typeConfig.name);
        },
      },
    },
    {
      field: 'category',
      title: '分类',
      width: 100,
      slots: {
        default: ({ row }: { row: Notification }) => {
          const categoryConfig = getNotificationCategoryConfig(row.category);
          return h(Tag, { color: categoryConfig.color }, () => categoryConfig.name);
        },
      },
    },
    {
      field: 'priority',
      title: '优先级',
      width: 100,
      slots: {
        default: ({ row }: { row: Notification }) => {
          const priorityConfig = getNotificationPriorityConfig(row.priority);
          return h(Tag, { color: priorityConfig.color }, () => priorityConfig.name);
        },
      },
    },
    {
      field: 'status',
      title: '状态',
      width: 100,
      slots: {
        default: ({ row }: { row: Notification }) => {
          const statusConfig = getNotificationStatusConfig(row.status);
          return h(Badge, {
            status: row.status === 0 ? 'error' : 'default',
            text: statusConfig.name,
          });
        },
      },
    },
    {
      field: 'recipients',
      title: '接收人数',
      width: 100,
      slots: {
        default: ({ row }: { row: Notification }) => {
          return h('span', {}, row.recipients?.length || 0);
        },
      },
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return new Date(cellValue).toLocaleString('zh-CN');
      },
    },
    {
      field: 'readAt',
      title: '已读时间',
      width: 160,
      formatter: ({ cellValue }: { cellValue?: string }) => {
        return cellValue ? new Date(cellValue).toLocaleString('zh-CN') : '-';
      },
    },
    {
      title: '操作',
      width: 150,
      fixed: 'right',
      slots: {
        default: ({ row }: { row: Notification }) => {
          return h(Space, { size: 'small' }, () => [
            h(Button, {
              type: 'text',
              size: 'small',
              onClick: () => onActionClick({ code: 'view', row }),
            }, () => [h(Eye, { class: 'w-4 h-4' })]),
            
            row.status === 0 && h(Button, {
              type: 'text',
              size: 'small',
              onClick: () => onActionClick({ code: 'markRead', row }),
            }, () => [h(CheckCircle, { class: 'w-4 h-4' })]),
            
            h(Button, {
              type: 'text',
              size: 'small',
              danger: true,
              onClick: () => onActionClick({ code: 'delete', row }),
            }, () => [h(Trash2, { class: 'w-4 h-4' })]),
          ]);
        },
      },
    },
  ];
}

/**
 * 通知模板列表表格列配置
 */
export function useNotificationTemplateColumns(
  onActionClick: (params: any) => void,
): VxeGridPropTypes.Columns {
  return [
    {
      type: 'checkbox',
      width: 50,
      fixed: 'left',
    },
    {
      field: 'code',
      title: '模板编码',
      width: 150,
      showOverflow: 'tooltip',
    },
    {
      field: 'name',
      title: '模板名称',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'title',
      title: '通知标题',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      field: 'type',
      title: '类型',
      width: 100,
      slots: {
        default: ({ row }: { row: NotificationTemplate }) => {
          const typeConfig = getNotificationTypeConfig(row.type);
          return h(Tag, { color: typeConfig.color }, () => typeConfig.name);
        },
      },
    },
    {
      field: 'category',
      title: '分类',
      width: 100,
      slots: {
        default: ({ row }: { row: NotificationTemplate }) => {
          const categoryConfig = getNotificationCategoryConfig(row.category);
          return h(Tag, { color: categoryConfig.color }, () => categoryConfig.name);
        },
      },
    },
    {
      field: 'isActive',
      title: '状态',
      width: 100,
      slots: {
        default: ({ row }: { row: NotificationTemplate }) => {
          return h(Badge, {
            status: row.isActive ? 'success' : 'default',
            text: row.isActive ? '启用' : '禁用',
          });
        },
      },
    },
    {
      field: 'variables',
      title: '变量数量',
      width: 100,
      slots: {
        default: ({ row }: { row: NotificationTemplate }) => {
          return h('span', {}, row.variables?.length || 0);
        },
      },
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 160,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return new Date(cellValue).toLocaleString('zh-CN');
      },
    },
    {
      title: '操作',
      width: 150,
      fixed: 'right',
      slots: {
        default: ({ row }: { row: NotificationTemplate }) => {
          return h(Space, { size: 'small' }, () => [
            h(Button, {
              type: 'text',
              size: 'small',
              onClick: () => onActionClick({ code: 'view', row }),
            }, () => [h(Eye, { class: 'w-4 h-4' })]),
            
            h(Button, {
              type: 'text',
              size: 'small',
              onClick: () => onActionClick({ code: 'edit', row }),
            }, () => '编辑'),
            
            h(Button, {
              type: 'text',
              size: 'small',
              danger: true,
              onClick: () => onActionClick({ code: 'delete', row }),
            }, () => [h(Trash2, { class: 'w-4 h-4' })]),
          ]);
        },
      },
    },
  ];
}

/**
 * 通知列表搜索表单配置
 */
export function useNotificationGridFormSchema() {
  return [
    {
      field: 'keyword',
      title: '关键词',
      component: 'Input',
      componentProps: {
        placeholder: '请输入标题或内容关键词',
      },
    },
    {
      field: 'status',
      title: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '未读', value: 0 },
          { label: '已读', value: 1 },
          { label: '已归档', value: 2 },
        ],
      },
    },
    {
      field: 'type',
      title: '类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择类型',
        options: [
          { label: '信息', value: 0 },
          { label: '警告', value: 1 },
          { label: '错误', value: 2 },
          { label: '成功', value: 3 },
        ],
      },
    },
    {
      field: 'category',
      title: '分类',
      component: 'Select',
      componentProps: {
        placeholder: '请选择分类',
        options: [
          { label: '系统', value: 0 },
          { label: '业务', value: 1 },
          { label: '安全', value: 2 },
          { label: '个人', value: 3 },
        ],
      },
    },
    {
      field: 'createTime',
      title: '创建时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
      },
    },
  ];
}

/**
 * 通知模板搜索表单配置
 */
export function useNotificationTemplateGridFormSchema() {
  return [
    {
      field: 'keyword',
      title: '关键词',
      component: 'Input',
      componentProps: {
        placeholder: '请输入模板名称或编码',
      },
    },
    {
      field: 'isActive',
      title: '状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: [
          { label: '启用', value: true },
          { label: '禁用', value: false },
        ],
      },
    },
    {
      field: 'type',
      title: '类型',
      component: 'Select',
      componentProps: {
        placeholder: '请选择类型',
        options: [
          { label: '信息', value: 0 },
          { label: '警告', value: 1 },
          { label: '错误', value: 2 },
          { label: '成功', value: 3 },
        ],
      },
    },
    {
      field: 'category',
      title: '分类',
      component: 'Select',
      componentProps: {
        placeholder: '请选择分类',
        options: [
          { label: '系统', value: 0 },
          { label: '业务', value: 1 },
          { label: '安全', value: 2 },
          { label: '个人', value: 3 },
        ],
      },
    },
  ];
}
