<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { onMounted, ref, watch } from 'vue'; // 添加了 onMounted 和 reactive 的导入
import { useRoute } from 'vue-router';

// 添加了 onMounted 和 reactive 的导入
import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import { downloadWitlabFile } from '#/api/core/witlab';
import {
  cancel,
  checkExistReportCOAApi,
  checkIfInspectionFailedApi,
  findDefaultCoATemplateApi,
  getBatchesListPage,
  StartBacthTwo,
  StartBatch,
  validateReleaseBatchApi,
} from '#/api/login-options/batch-manager';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import RawRecordForm from '#/views/common-apps/common-app/select-item-for-raw-record.vue';
import GeneralResults from '#/views/results-entry/general-results/general-results.vue';

import ApproveSamples from './approve-samples-results.vue';
import BatchAttach from './batch-attach.vue';
import {
  useBatchesColumns,
  useBatchesFilterSchema,
} from './batch-manager-data';
import BatchPass from './batch-pass.vue';
import BatchRecipe from './batch-recipe.vue';
import SampleRequirement from './batch-requirement.vue';
import SampleOrders from './batch-sample-orders.vue';
import CheckListForm from './check-list.vue';
import CopyForm from './copy-batch.vue';
import ReleaseBatchForm from './esig-with-comment-and-disposition.vue';
import LotGenealogyForm from './lot-genealogy-tree.vue';
import AddForm from './prompt-for-product-step.vue';
import BatchCoA from './report-coa.vue';
import SelectReportForm from './select-report-model.vue';

const activeKey = ref('tpSampleOrder');
const colums = useBatchesColumns();
const filterSchema = useBatchesFilterSchema();
const sMode = ref('');
const sStepCode = ref('');

const route = useRoute();
onMounted(async () => {
  if (route.query.Mode) {
    sMode.value = route.query.Mode as string;
    if (route.query.StepCode) {
      sStepCode.value = route.query.StepCode as string;
    }
  }
});

const queryData = async ({ page }: { page: any }) => {
  return await getBatchesListPage(
    sMode.value,
    '',
    sStepCode.value,
    page.pageSize,
    page.currentPage,
  );
};
// const queryData = async () => {
//   return getBatchesListPage(sMode.value, '', sStepCode.value);
// };
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<BatcheManagerApi.Batches>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 登录
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddForm,
});

async function onCreate() {
  formModalApi.setData(null).open();
}

// 复制
const [CopyFormModal, copyFormModalApi] = useVbenModal({
  connectedComponent: CopyForm,
});

async function onCopy() {
  const batch = gridApi.grid?.getCurrentRecord();
  if (!batch) return;
  copyFormModalApi.setData(batch).open();
}

// 启动
async function onStart() {
  // 获取选中行
  const batch = gridApi.grid?.getCurrentRecord();
  if (!batch) return;

  const batchId = batch.BATCHID;
  const sType = batch.TYPE;
  try {
    await confirm({
      title: '启动',
      content: `确定要启动批次 ${batchId} 吗？`,
      icon: 'warning',
      centered: false,
    });
    // 免检类批次
    if (sType === 'EPIM') {
      await StartBacthTwo(batchId);
    } else {
      const nStartBatch = await StartBatch(batchId);
      switch (nStartBatch) {
        case -400: {
          message.warn($t('login-options.batchManager.wrnSpecifications'));
          return;
        }
        case -300: {
          message.warn($t('login-options.batchManager.errSpecifications'));
          return;
        }
        case -200: {
          message.warn($t('login-options.batchManager.UNKNOWN_ERROR'));
          return;
        }
        case -101: {
          message.warn($t('login-options.batchManager.plannedPrecursors'));
          return;
        }
        case -100: {
          message.warn($t('login-options.batchManager.noBatchRecipe'));
          return;
        }
      }
    }
    message.success('启动成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 取消
async function onCancel() {
  // 获取选中行
  const batch = gridApi.grid?.getCurrentRecord();
  if (!batch) return;

  const batchId = batch.BATCHID;
  const sType = 'BATCH';
  try {
    await confirm({
      title: '取消',
      content: `确定要取消批次 ${batchId} 吗？`,
      icon: 'warning',
      centered: false,
    });

    await cancel(batchId, sType, null, '');
    message.success('操作成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 发布样品
const [ReleaseBatchFormModal, releaseBatchFormModalApi] = useVbenModal({
  connectedComponent: ReleaseBatchForm,
});

// 发布批次
async function onReleaseBatch() {
  if (CurrentRow.value === null) return;

  const sBatchId = CurrentRow.value.BATCHID;

  try {
    await confirm({
      title: '发布批次',
      content: `确定要发布批次 ${sBatchId} 吗？`,
      icon: 'warning',
      centered: false,
    });

    const aRet = await checkExistReportCOAApi(sBatchId);
    if (!aRet) {
      message.warn($t('login-options.batchManager.checkExistReportCOA'));
      return;
    }

    if (CurrentRow.value.ANSI_INSPECTION === 'Y') {
      if (CurrentRow.value.SUBMITTED_INSPECTION === 'N') {
        message.warn($t('login-options.batchManager.SubmitInspection'));
        return;
      }

      const bFailedInspection = await checkIfInspectionFailedApi(sBatchId);
      if (bFailedInspection) {
        await confirm({
          title: '发布',
          content: `批检查失败。是否要继续处理处置过程？`,
          icon: 'warning',
          centered: false,
        });
      }
    }

    const nValidate = await ValidatePriorToBatchRelease(sBatchId);
    if (nValidate < 0) return;

    releaseBatchFormModalApi
      .setData({
        MODE: 'BATCH',
        BATCHID: sBatchId,
      })
      .open();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

async function ValidatePriorToBatchRelease(sBatchId: number) {
  const nValidate = await validateReleaseBatchApi(sBatchId);
  if (nValidate === -100) {
    message.warn($t('login-options.batchManager.batchReleased'));
    return;
  }
  if (nValidate === -101) {
    message.warn($t('login-options.batchManager.disposeSamples'));
    return;
  }
  if (nValidate === -102) {
    message.warn($t('login-options.batchManager.APPROVE_SAMPLES'));
    return;
  }
  return nValidate;
}

// 合成原始记录
const [RawRecordFormModal, rawRecordFormModalApi] = useVbenModal({
  connectedComponent: RawRecordForm,
});
async function onRawRecordBT() {
  if (CurrentRow.value === null) return;
  rawRecordFormModalApi
    .setData({
      BATCHID: CurrentRow.value.BATCHID,
    })
    .open();
}
// 查看原始记录
const viewDocuments = async function () {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = CurrentRow.value.STARDOC_ID;
  downloadWitlabFile(docId, '原始记录.pdf');
};

// 检查清单
const [CheckListFormModal, checkListFormModalApi] = useVbenModal({
  connectedComponent: CheckListForm,
});
async function onCheckList() {
  if (CurrentRow.value === null) return;
  const readOnly =
    sMode.value === 'ReleaseOrder' || sMode.value === 'edit' ? 'Edit' : 'View';
  checkListFormModalApi
    .setData({
      BATCHID: CurrentRow.value.BATCHID,
      READONLY: readOnly,
    })
    .open();
}

// 预览COA
const [SelectReportFormModal, selectReportFormModalApi] = useVbenModal({
  connectedComponent: SelectReportForm,
});

// 预览COA
async function onPreviewCOA() {
  if (CurrentRow.value === null) return;
  const sBatchId = CurrentRow.value.BATCHID;
  const rv = await findDefaultCoATemplateApi(sBatchId);
  if (rv && rv[0]) {
    selectReportFormModalApi
      .setData({
        BATCHREPORTTYPE: 'Single',
        BATCHID: sBatchId,
        REPORT_TEMPLATE: rv[1],
        REPORTWAY: '',
        MODE: sMode.value,
      })
      .open();
  } else {
    selectReportFormModalApi
      .setData({
        BATCHREPORTTYPE: 'Single',
        BATCHID: sBatchId,
        REPORT_TEMPLATE: '',
        REPORTWAY: '',
        MODE: sMode.value,
      })
      .open();
  }
}

// 批家谱
const [LotGenealogyFormModal, lotGenealogyFormModalApi] = useVbenModal({
  connectedComponent: LotGenealogyForm,
});

// 批家谱
async function onLotGenealogy() {
  if (CurrentRow.value === null) return;
  lotGenealogyFormModalApi
    .setData({
      BATCHID: CurrentRow.value.BATCHID,
      BATCHNO: CurrentRow.value.BATCHNO,
    })
    .open();
}

const isShowCopy = ref(false);
const isShowRawRecordBT = ref(false);
const isShowViewDocuments = ref(false);
const isShowReleaseBatch = ref(false);
const isShowCheckList = ref(false);
const isShowEdit = ref(true);
const isShowCoATab = ref(false);
const isShowCancelBtn = ref(true);
const isShowAuditBtn = ref(false);
const isShowTpPass = ref(false);
const isShowTpApprove = ref(true);
const isShowTpResults = ref(false);
const isShowLotGenealogy = ref(true);

onMounted(() => {
  switch (sMode.value) {
    case 'Create': {
      activeKey.value = 'tpSampleOrder';
      isShowCopy.value = true;
      isShowTpApprove.value = false;
      break;
    }
    case 'Edit': {
      activeKey.value = 'tpApprove';
      isShowCheckList.value = true;
      isShowAuditBtn.value = true;
      isShowTpPass.value = true;
      isShowRawRecordBT.value = true;
      isShowViewDocuments.value = true;
      break;
    }
    case 'Inspection': {
      isShowTpApprove.value = false;
      isRecipeTabVisible.value = false;
      break;
    }
    case 'LevelApprove': {
      isShowCancelBtn.value = false;
      activeKey.value = 'tpPass';
      isShowTpPass.value = true;
      isShowTpResults.value = true;
      isShowCoATab.value = true;
      isOrderTabVisible.value = false;
      isRecipeTabVisible.value = false;
      isReqTabVisible.value = false;
      isShowLotGenealogy.value = false;
      break;
    }
    case 'LevelPass': {
      isShowCancelBtn.value = false;
      activeKey.value = 'tpPass';
      isShowTpPass.value = true;
      isShowTpResults.value = true;
      isShowCoATab.value = true;
      isOrderTabVisible.value = false;
      isRecipeTabVisible.value = false;
      isReqTabVisible.value = false;
      isShowLotGenealogy.value = false;
      break;
    }
    case 'MainFlowCOA': {
      activeKey.value = 'tpCoA';
      isShowCheckList.value = true;
      isShowCoATab.value = true;
      isShowCancelBtn.value = false;
      isShowTpResults.value = true;
      isRecipeTabVisible.value = false;

      // 报告编制
      if (sStepCode.value === 'Draft') {
        isShowAuditBtn.value = true;
        isShowRawRecordBT.value = true;
        isShowViewDocuments.value = true;
      }

      // 报告审核
      if (sStepCode.value === 'Approval') {
        isShowViewDocuments.value = true;
      }

      // 报告批准
      if (sStepCode.value === 'Release') {
        isShowViewDocuments.value = true;
      }
      break;
    }
    case 'Release': {
      activeKey.value = 'tpApprove';
      isShowReleaseBatch.value = true;
      isShowTpResults.value = true;
      isShowCoATab.value = true;
      break;
    }
    case 'ReleaseOrder': {
      activeKey.value = 'tpApprove';
      isShowRawRecordBT.value = true;
      isShowViewDocuments.value = true;
      isShowEdit.value = false;
      isShowCheckList.value = true;
      isShowTpResults.value = true;
      isRecipeTabVisible.value = false;
      break;
    }
    case 'Resent': {
      isShowTpApprove.value = false;
      isRecipeTabVisible.value = false;
      break;
    }
    case 'ResultsEntry': {
      isShowTpApprove.value = false;
      isRecipeTabVisible.value = false;
      break;
    }
    case 'View': {
      activeKey.value = 'tpApprove';
      isShowCheckList.value = true;
      isShowCancelBtn.value = false;
      isShowAuditBtn.value = true;
      isShowTpPass.value = true;
      isRecipeTabVisible.value = false;
      isShowRawRecordBT.value = true;
      isShowViewDocuments.value = true;
      break;
    }
    // No default
  }
});

const sParMode = ref<string>('');
const sClientWhr = ref<string>('');
const sRunNo = ref<any>(null);
const sFolderNo = ref<any>(null);
const sServgrp = ref<any>(null);
const sOrdno = ref<any>(null);
const nTestcode = ref<any>(null);
const nMethod = ref<any>(null);
const nBatchid = ref<any>(null);
const sShowAll = ref<any>(null);
const sMySamples = ref<any>(null);
const sRunType = ref<any>(null);
const oCTParams = ref<any>(null);
const bShowNotebook = ref<boolean>(true);
const sTraceabilityType = ref<string>('B');
const aTrendCTParams = ref<any>(null);
const sInvestigationType = ref<string>('BATCH');
const bAllowEditResult = ref<boolean>(true);
const sRefreshParentMethod = ref<any>(null);
const sOutsourceOrdno = ref<any>(null);
const sTestType = ref<string>('');
const bOpenEln = ref<boolean>(false);
const bIsRESTws = ref<boolean>(false);

// 新增响应式变量和 watcher
const isOrderTabVisible = ref(true);
const isRecipeTabVisible = ref(true);
const isReqTabVisible = ref(true);

watch(
  () => CurrentRow.value,
  async (_val) => {
    if (CurrentRow.value === null) return;

    const type = CurrentRow.value.TYPE;
    switch (type) {
      case 'EPIM': {
        if (sMode.value === 'Create') {
          activeKey.value = 'tpAttr';
          isOrderTabVisible.value = false;
          isRecipeTabVisible.value = false;
          isReqTabVisible.value = false;
        }

        break;
      }
      case 'FP': {
        isOrderTabVisible.value = true;

        if (sMode.value === 'Create') {
          activeKey.value = 'tpSampleOrder';
        } else if (
          sMode.value === 'View' ||
          sMode.value === 'ReleaseOrder' ||
          sMode.value === 'Release'
        ) {
          activeKey.value = 'tpApprove';
        }
        if (
          sMode.value !== 'Inspection' &&
          sMode.value !== 'MainFlowCOA' &&
          sMode.value !== 'LevelPass' &&
          sMode.value !== 'LevelApprove'
        ) {
          isRecipeTabVisible.value = true;
        }
        isReqTabVisible.value = true;
        break;
      }
      case 'RAW': {
        activeKey.value = 'tpSampleOrder';
        isOrderTabVisible.value = true;
        isRecipeTabVisible.value = false;
        isReqTabVisible.value = true;
        break;
      }
    }

    if (sMode.value === 'LevelPass' || sMode.value === 'LevelApprove') {
      isReqTabVisible.value = false;
    }

    const sBatchDispStatus = CurrentRow.value.DISPSTS;

    if (sMode.value === 'ResultsEntry') {
      sParMode.value = 'RESULTSENTRY';
    } else if (sMode.value === 'Edit') {
      sParMode.value = 'EDIT';
      if (
        sBatchDispStatus === 'Released' ||
        sBatchDispStatus === 'Restricted' ||
        sBatchDispStatus === 'ToBeReworked' ||
        sBatchDispStatus === 'Rejected' ||
        sBatchDispStatus === 'Cancelled'
      ) {
        sParMode.value = 'VIEW';
      }
    } else {
      sParMode.value = 'VIEW';
    }

    nBatchid.value = CurrentRow.value.BATCHID;

    if (sStepCode.value === 'Assigned') {
      sMySamples.value = 'TRUE';
    }

    if (
      sMode.value === 'VIEW' ||
      (sBatchDispStatus !== 'Started' &&
        sBatchDispStatus !== 'Planned' &&
        sBatchDispStatus !== 'Done')
    ) {
      bAllowEditResult.value = false;
    }
  },
);
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <CopyFormModal @success="onRefresh" />
    <ReleaseBatchFormModal @success="onRefresh" />
    <RawRecordFormModal @success="onRefresh" />
    <CheckListFormModal />
    <SelectReportFormModal />
    <LotGenealogyFormModal />
    <div class="flex h-full flex-col">
      <div class="h-1/2 w-full">
        <Grid>
          <template #toolbar-actions>
            <Space>
              <Button
                type="primary"
                @click="onCreate"
                v-if="sMode === 'Create'"
              >
                {{ $t('login-options.login') }}
              </Button>
              <Button type="default" @click="onCopy" v-if="isShowCopy">
                {{ $t('login-options.copy') }}
              </Button>
              <Button type="default" @click="onStart" v-if="sMode === 'Create'">
                {{ $t('login-options.start') }}
              </Button>
              <Button
                type="default"
                @click="onPreviewCOA"
                v-if="sMode === 'View'"
              >
                {{ $t('login-options.batchManager.previewCOA') }}
              </Button>
              <Button
                type="primary"
                @click="onReleaseBatch"
                v-if="isShowReleaseBatch"
              >
                {{ $t('login-options.batchManager.releaseBatch') }}
              </Button>
              <Button
                type="primary"
                danger
                @click="onCancel"
                v-if="isShowCancelBtn"
              >
                {{ $t('login-options.cancel') }}
              </Button>
              <Button
                type="default"
                @click="onLotGenealogy"
                v-if="isShowLotGenealogy"
              >
                {{ $t('login-options.batchManager.lotGenealogy') }}
              </Button>
              <Button
                type="default"
                @click="onRawRecordBT"
                v-if="isShowRawRecordBT"
              >
                {{ $t('login-options.batchManager.rawRecordBT') }}
              </Button>
              <Button
                type="default"
                @click="viewDocuments"
                v-if="isShowViewDocuments"
              >
                {{ $t('login-options.batchManager.viewRawRecordBT') }}
              </Button>
              <Button type="default" v-if="isShowAuditBtn">
                {{ $t('login-options.batchManager.prnAudit') }}
              </Button>
              <Button
                type="default"
                @click="onCheckList"
                v-if="isShowCheckList"
              >
                {{ $t('login-options.batchManager.checkList') }}
              </Button>
            </Space>
          </template>
          <template #action="{ row }">
            <template v-if="hasEditStatus(row)">
              <Button type="link" @click="saveRowEvent(row)">
                {{ $t('login-options.save') }}
              </Button>
              <Button type="link" @click="cancelRowEvent(row)">
                {{ $t('login-options.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button type="link" @click="editRowEvent(row)" v-if="isShowEdit">
                {{ $t('login-options.edit') }}
              </Button>
            </template>
          </template>
        </Grid>
      </div>
      <div class="h-1/2 flex-1 bg-white px-5">
        <Tabs v-model:active-key="activeKey" class="h-full">
          <TabPane key="tpApprove" tab="发布" v-if="isShowTpApprove">
            <ApproveSamples
              :current-test-row="CurrentRow"
              :mode="sMode"
              :step-code="sStepCode"
              @refresh="onRefresh"
            />
          </TabPane>
          <TabPane key="tpSampleOrder" tab="样品/测试" v-if="isOrderTabVisible">
            <SampleOrders
              :current-test-row="CurrentRow"
              :mode="
                typeof route.query.Mode === 'string' ? route.query.Mode : ''
              "
            />
          </TabPane>
          <TabPane key="tpBatchRecipe" tab="配方" v-if="isRecipeTabVisible">
            <BatchRecipe
              :current-test-row="CurrentRow"
              :mode="
                typeof route.query.Mode === 'string' ? route.query.Mode : ''
              "
            />
          </TabPane>
          <TabPane key="tpRequirement" tab="取样要求" v-if="isReqTabVisible">
            <SampleRequirement :current-test-row="CurrentRow" :mode="sMode" />
          </TabPane>
          <TabPane key="tpResults" tab="结果" v-if="isShowTpResults">
            <GeneralResults
              :s-mode="sParMode"
              :s-runno="sRunNo"
              :s-folder-no="sFolderNo"
              :s-servgrp="sServgrp"
              :s-ordno="sOrdno"
              :n-testcode="nTestcode"
              :n-method="nMethod"
              :n-batchid="nBatchid"
              :s-show-all="sShowAll"
              :s-my-samples="sMySamples"
              :s-run-type="sRunType"
              :s-client-whr="sClientWhr"
              :o-c-t-params="oCTParams"
              :b-show-notebook="bShowNotebook"
              :s-traceability-type="sTraceabilityType"
              :a-trend-c-t-params="aTrendCTParams"
              :s-investigation-type="sInvestigationType"
              :b-allow-edit-result="bAllowEditResult"
              :s-refresh-parent-method="sRefreshParentMethod"
              :s-outsource-ordno="sOutsourceOrdno"
              :s-test-type="sTestType"
              :b-open-eln="bOpenEln"
              :b-is-rest-ws="bIsRESTws"
            />
          </TabPane>
          <TabPane key="tpCoA" tab="COA" v-if="isShowCoATab">
            <BatchCoA
              :current-test-row="CurrentRow"
              :mode="sMode"
              :step-code="sStepCode"
            />
          </TabPane>
          <TabPane key="tpAttr" tab="附件">
            <BatchAttach :current-test-row="CurrentRow" :mode="sMode" />
          </TabPane>
          <TabPane key="tpPass" tab="放行单" v-if="isShowTpPass">
            <BatchPass
              :current-test-row="CurrentRow"
              :mode="sMode"
              :step-code="sStepCode"
            />
          </TabPane>
        </Tabs>
      </div>
    </div>
  </Page>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
