<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { Notification } from '#/types/notification';

import { ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { Plus, Download, RefreshCw } from '@vben/icons';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { notificationApi } from '#/api/notification';
import { $t } from '#/locales';

import { useNotificationColumns, useNotificationGridFormSchema } from './data';
import DetailModal from './modules/detail.vue';

const [DetailModalComponent, detailModalApi] = useVbenModal({
  connectedComponent: DetailModal,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useNotificationGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useNotificationColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page: any }, formValues: any) => {
          const response = await notificationApi.getNotifications({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            result: response.items,
            page: {
              total: response.totalCount,
            },
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
      isCurrent: true,
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
    params: {
      tableName: 'Notification',
    },
  } as VxeTableGridOptions<Notification>,
});

function onActionClick(e: OnActionClickParams<Notification>) {
  switch (e.code) {
    case 'view': {
      onView(e.row);
      break;
    }
    case 'markRead': {
      onMarkRead(e.row);
      break;
    }
    case 'delete': {
      onDelete(e.row);
      break;
    }
  }
}

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 */
function confirm(content: string, title: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        resolve(true);
      },
      title,
    });
  });
}

function onView(row: Notification) {
  detailModalApi.setData(row).open();
}

async function onMarkRead(row: Notification) {
  try {
    message.loading({
      content: '正在标记为已读...',
      duration: 0,
      key: 'mark_read_msg',
    });

    await notificationApi.markAsRead(row.id);

    message.success({
      content: '已标记为已读',
      key: 'mark_read_msg',
    });

    onRefresh();
  } catch {
    message.error({
      content: '标记失败',
      key: 'mark_read_msg',
    });
  }
}

async function onDelete(row: Notification) {
  try {
    await confirm(
      `确定要删除通知"${row.content.title}"吗？`,
      '删除确认',
    );

    message.loading({
      content: '正在删除...',
      duration: 0,
      key: 'delete_msg',
    });

    await notificationApi.deleteNotification(row.id);

    message.success({
      content: '删除成功',
      key: 'delete_msg',
    });

    onRefresh();
  } catch (error: any) {
    if (error.message !== '已取消') {
      message.error({
        content: '删除失败',
        key: 'delete_msg',
      });
    }
  }
}

async function onBatchMarkRead() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要标记的通知');
    return;
  }

  try {
    await confirm(
      `确定要将选中的 ${selectedRows.length} 条通知标记为已读吗？`,
      '批量标记确认',
    );

    message.loading({
      content: '正在批量标记...',
      duration: 0,
      key: 'batch_mark_msg',
    });

    const ids = selectedRows.map((row) => row.id);
    await notificationApi.batchOperation({ ids, operation: 'read' });

    message.success({
      content: '批量标记成功',
      key: 'batch_mark_msg',
    });

    onRefresh();
  } catch (error: any) {
    if (error.message !== '已取消') {
      message.error({
        content: '批量标记失败',
        key: 'batch_mark_msg',
      });
    }
  }
}

async function onBatchDelete() {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要删除的通知');
    return;
  }

  try {
    await confirm(
      `确定要删除选中的 ${selectedRows.length} 条通知吗？此操作不可恢复！`,
      '批量删除确认',
    );

    message.loading({
      content: '正在批量删除...',
      duration: 0,
      key: 'batch_delete_msg',
    });

    const ids = selectedRows.map((row) => row.id);
    await notificationApi.batchOperation({ ids, operation: 'delete' });

    message.success({
      content: '批量删除成功',
      key: 'batch_delete_msg',
    });

    onRefresh();
  } catch (error: any) {
    if (error.message !== '已取消') {
      message.error({
        content: '批量删除失败',
        key: 'batch_delete_msg',
      });
    }
  }
}

async function onExport() {
  try {
    message.loading({
      content: '正在导出...',
      duration: 0,
      key: 'export_msg',
    });

    const response = await notificationApi.exportNotifications({
      format: 'excel',
    });

    // 创建下载链接
    const link = document.createElement('a');
    link.href = response.downloadUrl;
    link.download = response.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success({
      content: '导出成功',
      key: 'export_msg',
    });
  } catch {
    message.error({
      content: '导出失败',
      key: 'export_msg',
    });
  }
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  // 跳转到创建通知页面或打开创建弹窗
  message.info('创建通知功能开发中...');
}

const createButtonRef = ref();
const createCode = 'System.Notification.Create';
</script>

<template>
  <Page auto-content-height>
    <DetailModalComponent />
    <Grid>
      <template #toolbarButtons>
        <Space>
          <Button
            ref="createButtonRef"
            type="primary"
            @click="onCreate"
            v-access:code="[createCode]"
          >
            <Plus class="size-4" />
            {{ $t('ui.actionTitle.create', ['通知']) }}
          </Button>
          <Button @click="onBatchMarkRead">
            <RefreshCw class="size-4" />
            批量已读
          </Button>
          <Button danger @click="onBatchDelete">
            删除选中
          </Button>
          <Button @click="onExport">
            <Download class="size-4" />
            导出
          </Button>
        </Space>
      </template>
    </Grid>
  </Page>
</template>
