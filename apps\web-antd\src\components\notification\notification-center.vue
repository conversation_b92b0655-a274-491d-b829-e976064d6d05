<template>
  <div class="notification-center">
    <!-- 触发按钮 -->
    <Popover
      v-model:open="visible"
      placement="bottomRight"
      trigger="click"
      :overlay-class-name="'notification-center__popover'"
    >
      <template #content>
        <div class="notification-center__panel">
          <!-- 头部 -->
          <div class="notification-center__header">
            <div class="notification-center__title">
              <Bell class="w-4 h-4 mr-2" />
              消息通知
              <Badge
                v-if="unreadCount > 0"
                :count="unreadCount"
                :number-style="{ backgroundColor: '#ff4d4f' }"
                class="ml-2"
              />
            </div>
            <div class="notification-center__actions">
              <Button
                v-if="showMarkAllButton && unreadCount > 0"
                type="text"
                size="small"
                @click="handleMarkAllRead"
              >
                <CheckCircle class="w-4 h-4 mr-1" />
                全部已读
              </Button>
              <Button
                type="text"
                size="small"
                @click="handleRefresh"
              >
                <RotateCcw class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <!-- 标签页 -->
          <Tabs
            v-model:activeKey="activeTab"
            size="small"
            @change="handleTabChange"
          >
            <TabPane key="all" tab="全部">
              <NotificationList
                :notifications="filteredNotifications"
                :loading="loading"
                :has-more="hasMore"
                :show-actions="true"
                :show-avatar="true"
                :show-time="true"
                :show-category="showCategories"
                @notification-click="handleNotificationClick"
                @mark-read="handleMarkRead"
                @delete="handleDelete"
                @load-more="handleLoadMore"
              />
            </TabPane>
            <TabPane key="unread" tab="未读">
              <NotificationList
                :notifications="unreadNotifications"
                :loading="loading"
                :has-more="false"
                :show-actions="true"
                :show-avatar="true"
                :show-time="true"
                :show-category="showCategories"
                @notification-click="handleNotificationClick"
                @mark-read="handleMarkRead"
                @delete="handleDelete"
              />
            </TabPane>
          </Tabs>

          <!-- 底部操作 -->
          <div class="notification-center__footer">
            <Button
              v-if="showClearButton"
              type="text"
              size="small"
              danger
              @click="handleClearAll"
            >
              清空所有
            </Button>
            <Button
              type="primary"
              size="small"
              @click="handleViewAll"
            >
              查看全部
            </Button>
          </div>
        </div>
      </template>

      <!-- 触发器 -->
      <div class="notification-center__trigger">
        <Badge
          :count="unreadCount"
          :number-style="{ backgroundColor: '#ff4d4f' }"
          :offset="[-2, 2]"
        >
          <Button
            type="text"
            shape="circle"
            :class="{
              'notification-center__trigger--active': visible,
              'notification-center__trigger--has-unread': unreadCount > 0,
            }"
            @click="handleToggle"
          >
            <Bell
              class="w-5 h-5"
              :class="{
                'notification-center__bell--ring': unreadCount > 0 && !visible,
              }"
            />
          </Button>
        </Badge>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import type { Notification, NotificationStatus } from '#/types/notification';
import type { NotificationCenterEvents, NotificationCenterProps } from './types';

import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Badge, Button, Popover, TabPane, Tabs } from 'ant-design-vue';
import { Bell, CheckCircle, RotateCcw } from 'lucide-vue-next';

import { useNotificationStore } from '#/store/notification';

import NotificationList from './notification-list.vue';

// Props定义
const props = withDefaults(defineProps<NotificationCenterProps>(), {
  maxHeight: 400,
  showCategories: true,
  autoRefresh: false,
  refreshInterval: 30000,
  showClearButton: true,
  showMarkAllButton: true,
});

// 事件定义
const emit = defineEmits<NotificationCenterEvents>();

// 路由
const router = useRouter();

// 状态管理
const notificationStore = useNotificationStore();

// 响应式状态
const visible = ref(false);
const activeTab = ref<'all' | 'unread'>('all');
const refreshTimer = ref<number>();

// 计算属性
const unreadCount = computed(() => notificationStore.unreadCount);
const loading = computed(() => notificationStore.loading);
const hasMore = computed(() => notificationStore.hasMore);

const filteredNotifications = computed(() => {
  if (activeTab.value === 'unread') {
    return notificationStore.unreadNotifications;
  }
  return notificationStore.filteredNotifications;
});

const unreadNotifications = computed(() => notificationStore.unreadNotifications);

// 生命周期
onMounted(async () => {
  // 初始化通知系统
  await notificationStore.initialize();
  
  // 设置自动刷新
  if (props.autoRefresh) {
    startAutoRefresh();
  }
});

onUnmounted(() => {
  stopAutoRefresh();
});

// 监听自动刷新配置变化
watch(
  () => props.autoRefresh,
  (newValue) => {
    if (newValue) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  },
);

// 方法
function startAutoRefresh() {
  stopAutoRefresh();
  refreshTimer.value = window.setInterval(() => {
    if (!visible.value) {
      notificationStore.refresh();
    }
  }, props.refreshInterval);
}

function stopAutoRefresh() {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = undefined;
  }
}

// 事件处理
function handleToggle() {
  visible.value = !visible.value;
  
  if (visible.value) {
    // 打开时刷新数据
    notificationStore.refresh();
  }
}

function handleTabChange(key: string) {
  activeTab.value = key as 'all' | 'unread';
}

function handleNotificationClick(notification: Notification) {
  emit('notification-click', notification);
  
  // 自动标记为已读
  if (notification.status === NotificationStatus.Unread) {
    handleMarkRead(notification.id);
  }
}

function handleMarkRead(id: string) {
  notificationStore.markAsRead(id);
  emit('mark-read', id);
}

function handleMarkAllRead() {
  notificationStore.markAllAsRead();
  emit('mark-all-read');
}

function handleDelete(id: string) {
  notificationStore.deleteNotification(id);
  emit('delete', id);
}

function handleClearAll() {
  notificationStore.clearAll();
  emit('clear-all');
}

function handleLoadMore() {
  notificationStore.loadMore();
  emit('load-more');
}

function handleRefresh() {
  notificationStore.refresh();
  emit('refresh');
}

function handleViewAll() {
  // 跳转到通知管理页面
  router.push('/system/notification');
  visible.value = false;
}
</script>

<style scoped>
.notification-center {
  @apply relative;
}

.notification-center__trigger {
  @apply flex items-center justify-center;
}

.notification-center__trigger--active {
  @apply bg-blue-50 text-blue-600;
}

.notification-center__trigger--has-unread {
  @apply text-red-500;
}

.notification-center__bell--ring {
  animation: bell-ring 1s ease-in-out;
}

.notification-center__panel {
  @apply w-80 max-h-96 bg-white rounded-lg shadow-lg;
}

.notification-center__header {
  @apply flex items-center justify-between p-4 border-b border-gray-100;
}

.notification-center__title {
  @apply flex items-center font-medium text-gray-900;
}

.notification-center__actions {
  @apply flex items-center gap-1;
}

.notification-center__footer {
  @apply flex items-center justify-between p-3 border-t border-gray-100;
}

/* 铃铛摇摆动画 */
@keyframes bell-ring {
  0%, 100% {
    transform-origin: top center;
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(10deg);
  }
  20% {
    transform: rotate(-10deg);
  }
  30% {
    transform: rotate(10deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  50% {
    transform: rotate(5deg);
  }
  60% {
    transform: rotate(-5deg);
  }
  70% {
    transform: rotate(2deg);
  }
  80% {
    transform: rotate(-2deg);
  }
  90% {
    transform: rotate(1deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-center__panel {
    @apply w-72;
  }
}
</style>

<style>
.notification-center__popover .ant-popover-content {
  padding: 0 !important;
}

.notification-center__popover .ant-popover-inner {
  padding: 0 !important;
}
</style>
