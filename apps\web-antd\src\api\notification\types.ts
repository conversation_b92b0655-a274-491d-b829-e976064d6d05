/**
 * 通知API类型定义
 * 与后端API接口保持一致
 */

import type { Notification, NotificationTemplate } from '#/types/notification';

/**
 * 通知API命名空间
 */
export namespace NotificationApi {
  // 基础响应类型
  export interface BaseResponse<T = any> {
    code: number;
    message: string;
    data: T;
    success: boolean;
  }

  // 分页请求参数
  export interface PaginationParams {
    page?: number;
    pageSize?: number;
    skip?: number;
    take?: number;
  }

  // 获取通知列表请求
  export interface GetNotificationsParams extends PaginationParams {
    status?: number;
    category?: number;
    type?: number;
    startTime?: string;
    endTime?: string;
    keyword?: string;
  }

  // 获取通知列表响应
  export interface GetNotificationsResponse {
    items: Notification[];
    totalCount: number;
    hasMore: boolean;
  }

  // 获取通知详情响应
  export interface GetNotificationResponse {
    notification: Notification;
  }

  // 创建通知响应
  export interface CreateNotificationResponse {
    id: string;
    success: boolean;
    message: string;
  }

  // 标记已读响应
  export interface MarkAsReadResponse {
    success: boolean;
    message: string;
  }

  // 获取未读数量响应
  export interface GetUnreadCountResponse {
    unreadCount: number;
  }

  // 获取通知模板列表请求
  export interface GetTemplatesParams extends PaginationParams {
    isActive?: boolean;
    category?: number;
    keyword?: string;
  }

  // 获取通知模板列表响应
  export interface GetTemplatesResponse {
    items: NotificationTemplate[];
    totalCount: number;
    hasMore: boolean;
  }

  // 获取通知模板详情响应
  export interface GetTemplateResponse {
    template: NotificationTemplate;
  }

  // 创建通知模板请求
  export interface CreateTemplateRequest {
    code: string;
    name: string;
    title: string;
    content: string;
    type: number;
    category: number;
    priority: number;
    variables: Array<{
      defaultValue?: string;
      displayName: string;
      isRequired: boolean;
      name: string;
    }>;
  }

  // 更新通知模板请求
  export interface UpdateTemplateRequest extends CreateTemplateRequest {
    id: string;
  }

  // 删除通知模板响应
  export interface DeleteTemplateResponse {
    success: boolean;
    message: string;
  }

  // 批量操作请求
  export interface BatchOperationRequest {
    ids: string[];
    operation: 'archive' | 'delete' | 'read' | 'unread';
  }

  // 批量操作响应
  export interface BatchOperationResponse {
    success: boolean;
    message: string;
    affectedCount: number;
  }

  // 通知统计请求
  export interface GetStatisticsParams {
    startTime?: string;
    endTime?: string;
    groupBy?: 'day' | 'month' | 'week';
  }

  // 通知统计响应
  export interface GetStatisticsResponse {
    totalCount: number;
    unreadCount: number;
    readCount: number;
    archivedCount: number;
    categoryStats: Array<{
      category: number;
      count: number;
    }>;
    typeStats: Array<{
      count: number;
      type: number;
    }>;
    timeStats: Array<{
      count: number;
      date: string;
    }>;
  }

  // 用户偏好设置请求
  export interface UpdatePreferencesRequest {
    emailNotification: boolean;
    pushNotification: boolean;
    categories: number[];
    types: number[];
    quietHours: {
      enabled: boolean;
      endTime: string;
      startTime: string;
    };
  }

  // 用户偏好设置响应
  export interface GetPreferencesResponse {
    emailNotification: boolean;
    pushNotification: boolean;
    categories: number[];
    types: number[];
    quietHours: {
      enabled: boolean;
      endTime: string;
      startTime: string;
    };
  }

  // 导出通知请求
  export interface ExportNotificationsRequest {
    format: 'csv' | 'excel' | 'pdf';
    startTime?: string;
    endTime?: string;
    status?: number;
    category?: number;
    type?: number;
  }

  // 导出通知响应
  export interface ExportNotificationsResponse {
    downloadUrl: string;
    fileName: string;
    fileSize: number;
  }

  // 通知订阅请求
  export interface SubscribeRequest {
    categories: number[];
    types: number[];
    keywords: string[];
  }

  // 通知订阅响应
  export interface SubscribeResponse {
    success: boolean;
    message: string;
    subscriptionId: string;
  }

  // 取消订阅请求
  export interface UnsubscribeRequest {
    subscriptionId: string;
  }

  // 取消订阅响应
  export interface UnsubscribeResponse {
    success: boolean;
    message: string;
  }
}

/**
 * API错误类型
 */
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

/**
 * API请求配置
 */
export interface ApiRequestConfig {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheTTL?: number;
}

/**
 * 文件上传类型
 */
export interface FileUploadRequest {
  file: File;
  category?: number;
  description?: string;
}

export interface FileUploadResponse {
  url: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
}

/**
 * WebSocket消息类型
 */
export interface WebSocketMessage {
  type: 'connection' | 'error' | 'notification' | 'unread_count';
  data: any;
  timestamp: string;
}

/**
 * 实时通知数据
 */
export interface RealtimeNotificationData {
  notification: Notification;
  userId: string;
  timestamp: string;
}

/**
 * 实时未读数量数据
 */
export interface RealtimeUnreadCountData {
  unreadCount: number;
  userId: string;
  timestamp: string;
}
