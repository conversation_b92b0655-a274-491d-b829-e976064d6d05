<script setup lang="ts">
import type {
  UploadChangeParam,
  UploadFile,
  UploadProps,
} from 'ant-design-vue';
import type { VxeTableDefines } from 'vxe-table';

import type { Recordable } from '@vben/types';

import type { XFolderApi } from '#/api/business-static-tables/study-template';
import type { StabilityStudiesApi } from '#/api/stability-studies';

import { computed, nextTick, ref, watch } from 'vue';

import {
  alert,
  Page,
  prompt,
  useVbenDrawer,
  useVbenModal,
} from '@vben/common-ui';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@vben-core/shadcn-ui';

import {
  Button,
  InputNumber,
  message,
  Radio,
  RadioGroup,
  Space,
  TabPane,
  Tabs,
  Upload,
} from 'ant-design-vue';

import {
  $createXorders_XordTaskApi,
  $getStabilityArrayDataApi,
  $scFillMatrixApi,
} from '#/api/business-static-tables/study-template';
import {
  downloadWitlabFile,
  getServerTempFilePathByWitlabId,
  UploadWitlabTempFile,
} from '#/api/core/witlab';
import { $enterpriseUtilitiesDeleteAttachmentApi } from '#/api/enterprise-utilities';
import {
  $changeAnalytesListApi,
  $reOpenOrderApi,
} from '#/api/sample-login/samplelogin-general';
import {
  $addXAttachmentApi,
  $changeChamberLocationApi,
  $checkLayoutSampleApi,
  $chkReportApi,
  $conditionQtyApi,
  $createReportApi,
  $createStabCOAApi,
  $delStabilityReportApi,
  $dsGetProjectedTestApi,
  $getAttachmentsApi,
  $getConditionsApi,
  $getReportByStabnoApi,
  $getReportNoByPlanNoApi,
  $getResultsApi,
  $getWordTempApi,
  $pullLeftoverContainersApi,
  $regenerateStabCOAApi,
  $retireStabReportApi,
  $returnInChamberApi,
  $scUpdateExtaQtyApi,
  $stabRetReusultsApi,
  $sumPerConditionApi,
  $syncT0Results_ValidateApi,
  $syncT0ResultsApi,
  $UDInventoryApi,
  $updateStabOrdersTestsApi,
  $updExtraQtyApi,
  $validateEditLayoutApi,
  $validateReturnApi,
} from '#/api/stability-studies';
import { $updLocation } from '#/api/stability-studies/stability-inventory';
import { esignBegin } from '#/components/esig';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';
import AddStudyWizard from '#/views/business-static-tables/study-configuration/add-study-wizard.vue';
import StabnoIntervalsTat from '#/views/business-static-tables/study-configuration/stabno-intervals-tat.vue';
// 结果附件
import AttachForm from '#/views/common-apps/eln-running/attachment-chrom.vue';
import RetestRequestFc from '#/views/results-entry/retest-request/retest-request-fc.vue';
import ViewRetestForm from '#/views/results-entry/retest-request/view-retest.vue';
import Signatures from '#/views/run-create-results-entry-run-approv/traceability-view/signatures.vue';
import TraceabilityView from '#/views/run-create-results-entry-run-approv/traceability-view/traceability-view.vue';
// 查看方法
import MethodVersionInfo from '#/views/runbuild-resent-approve/method-version-info.vue';
import EditAnalytesList from '#/views/sample-login/samplelogin-general/edit-analytes-list.vue';
import { showAuditModal } from '#/views/utilities/general-workflow-actions';

// 同步结果 btnSyncT0
import AssignZeroInterval from '../assign-zero-interval.vue';
import CreateStabilityReport from '../create-stability-report.vue';
import {
  useAttachmentColumns,
  useCondIntTestMatrixColumns,
  useConditionQtyColumns,
  useReportColumns,
  useResultColumns,
  useSumTestPerConditionColumns,
  useTestListColumns,
} from '../data';
import DlgChangeDeptSrvgrp from '../dlg-change-dept-srvgrp.vue';
import GetInventory from '../get-inventory.vue';
import GetLocation from '../get-location.vue';
import SelBackRes from '../sel-back-res.vue';
import SelectStabReportModel from '../select-stabReport-model.vue';
import UpdStabInv from '../upd-stab-inv.vue';

const emit = defineEmits(['refresh']);
const activeKey = ref('1');
const formVar = defineModel<Record<string, any>>('formVar', { default: {} }); // formVar.Variables变量，双向绑定
const studyRowData = ref<StabilityStudiesApi.XFolder>(); // 稳定性研究数据
const buttonConfigs = {
  btnRetireStabReport: {
    visible: () =>
      mode.value === 'StabilityReport' && formVar.value.StepCode === 'Draft',
    enabled: () => reportCurrentRow.value?.STATUS === 'Done',
  },
  btnFill: {
    visible: () => mode.value === 'Create' || mode.value === 'Edit',
    enabled: () => {
      return (
        studyRowData.value?.FDISPSTAT &&
        studyRowData.value.FDISPSTAT !== 'Stopped' &&
        studyRowData.value.FDISPSTAT !== 'Completed'
      );
    },
  },
  Edit_btn: {
    visible: () => mode.value === 'Create' || mode.value === 'Edit',
    enabled: () =>
      studyRowData.value?.FDISPSTAT &&
      studyRowData.value?.FDISPSTAT !== 'Stopped' &&
      studyRowData.value?.FDISPSTAT !== 'Completed',
  },
  btnIntervalsTat: {
    visible: () =>
      (mode.value === 'Create' || mode.value === 'Edit') &&
      studyRowData.value?.CYCLE === null,
    enabled: () =>
      studyRowData.value?.FDISPSTAT &&
      studyRowData.value?.FDISPSTAT !== 'Stopped' &&
      studyRowData.value?.FDISPSTAT !== 'Completed',
  },
  btnSetLongCode: {
    visible: () =>
      !(
        mode.value === 'Review Study' ||
        mode.value === 'View' ||
        mode.value === 'Complete'
      ),
  },
  btnSetExtraQty: {
    visible: () =>
      studyRowData.value?.FDISPSTAT &&
      studyRowData.value?.FDISPSTAT !== 'Started' &&
      studyRowData.value?.FDISPSTAT !== 'Completed' &&
      studyRowData.value?.FDISPSTAT !== 'Stopped' &&
      (mode.value === 'Create' ||
        mode.value === 'Sampling Study' ||
        mode.value === 'Edit'),
  },
  btnDelStabReport: {
    visible: () =>
      mode.value === 'StabilitySumReport' || formVar.value.StepCode === 'Draft',
  },
  btnSubmitStabReport: {
    visible: () =>
      mode.value === 'StabilityReport' && formVar.value.StepCode !== 'Done',
  },
  btnBackRes: {
    visible: () =>
      mode.value === 'StabilityReport' && formVar.value.StepCode === 'Draft',
  },
  btnSetMethod: {
    visible: () => mode.value !== 'View',
    enabled: () =>
      testListCurrentRow.value &&
      (testListCurrentRow.value?.TS === 'Draft' ||
        testListCurrentRow.value?.TS === 'Started' ||
        testListCurrentRow.value?.TS === 'BACKUP'),
  },
  btnReturnChamber: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      SumTestPerConditionCurrentRow.value &&
      studyRowData.value &&
      studyRowData.value.FDISPSTAT !== 'Completed' &&
      SumTestPerConditionCurrentRow.value.PULLDATE !== null,
  },
  btnPullContainer: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      SumTestPerConditionCurrentRow.value &&
      studyRowData.value &&
      SumTestPerConditionCurrentRow.value.PULLDATE !== null &&
      SumTestPerConditionCurrentRow.value.NUMCONT >
        SumTestPerConditionCurrentRow.value.CONTPULLED,
  },
  btnExtra: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      SumTestPerConditionCurrentRow.value &&
      SumTestPerConditionCurrentRow.value.PULLDATE !== null &&
      ConditionQtyCurrentRow.value?.EXTRAQTY &&
      ConditionQtyCurrentRow.value.EXTRAQTY > 0,
  },
  btnInventory: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      SumTestPerConditionCurrentRow.value &&
      SumTestPerConditionCurrentRow.value.PULLDATE !== null,
  },
  btnlocation: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      SumTestPerConditionCurrentRow.value &&
      SumTestPerConditionCurrentRow.value.PULLDATE !== null,
  },
  btnConfirmatory: {
    visible: () => mode.value === 'Edit',
  },
  btnViewConfirmatory: {
    enabled: () =>
      resultCurrentRow.value && resultCurrentRow.value.RETESTFLAG === '!',
  },
  btnEditAnalytesList: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      resultCurrentRow.value && resultCurrentRow.value.S === 'Logged',
  },
  btnSyncT0: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      resultCurrentRow.value &&
      studyRowData.value &&
      studyRowData.value.FDISPSTAT !== 'Completed' &&
      studyRowData.value.FDISPSTAT !== 'Stopped',
  },
  btnReopenSample: {
    visible: () => mode.value === 'Edit',
    enabled: () =>
      resultCurrentRow.value &&
      studyRowData.value &&
      (studyRowData.value.FDISPSTAT === 'Started' ||
        studyRowData.value.FDISPSTAT === 'Logged'),
  },
};

const AddedSymbol = 'test';
const BackupSymbol = 'backup';
const LateSymbol = 'late';
const ScheduledSymbol = 'scheduled';
const CancelledSymbol = 'cancelled';
const PulledOutsideSymbol = 'pulledOutside';
const PulledSymbolSymbol = 'pulled';
const StoppedSymbol = 'stopped';
const ReleasedSymbol = 'released';
const ResultedSymbol = 'resulted';
const TPReleasedSymbol = 'TPreleased';

const matrixType = ref<string>('test');
const oArg = ref({ sMode: 'STABILITY_PROTOCOL', aDsParam: [-1] });

const SubStudySymbol =
  '|||RUNTIME_SUPPORT.GetImageById.lims?ImageId=75C6C0BC-9A60-4247-B630-2F355DD5EC32';
const signaturesRef = ref<InstanceType<typeof Signatures>>();
const mode = ref<string>(); // 页面模式
const wizardMode = 'LIFECYCLE'; // 这是一个固定参数
const headers = {
  authorization: 'authorization-text',
};
const fileList = ref<UploadProps['fileList']>([]); // 文件列表

const {
  Grid: CondIntTestMatrixGrid,
  gridApi: condIntTestMatrixGridApi,
  CurrentRow: MatrixCurrentRow,
} = useLimsGridsConfig<XFolderApi.CondIntTestMatrix>(
  useCondIntTestMatrixColumns(),
  [],
  () => {
    return Promise.resolve([]);
  },
  {
    pagerConfig: {
      enabled: false,
    },
    toolbarConfig: {
      export: true,
      zoom: true,
    },
    rowGroupConfig: {
      groupFields: ['Col4'],
      trigger: 'row',
    },
    keepSource: false,
  },
  {
    cellDblclick: async ({ row, column }) => {
      /**
       * 		
    if(form.Variables["Mode"] != "Create" && form.Variables["Mode"] != 'Edit'){
		    CondIntTestMatrix_gd.OnDoubleClick = "";
		}
       */
      if (mode.value !== 'Create' && mode.value !== 'Edit') {
        return;
      }
      const oRet = await validateEditLayout(row, column);
      if (!oRet.bValid) {
        return;
      }
      if (!studyRowData.value) {
        return;
      }
      const bRemoveCondition = oRet.bRemoveCondition; // 是否移除条件
      const bStartStudy = oRet.bStartStudy; // 是否开始稳定性研究
      const StabNo = studyRowData.value.STABNO;
      const Condition = row.Col4;
      const colName = column.field;
      const col = column;
      const Interval = col.title;
      const Testcode = row.Col2 as number;
      let nNumCont = 0;
      const CurrentSymbol = row[colName] as string;
      // if (!CurrentSymbol.startsWith('|||') && CurrentSymbol.length > 1) {
      //   const nStart = CurrentSymbol.indexOf('|||');
      //   const nLength = CurrentSymbol.length - nStart;
      //   CurrentSymbol = CurrentSymbol.slice(nStart, nStart + nLength);
      // }
      let Symbol = '';
      let sComment = '';
      switch (CurrentSymbol) {
        case AddedSymbol: {
          Symbol = 'Added';
          break;
        }

        case BackupSymbol: {
          Symbol = 'BackUp';
          break;
        }

        case CancelledSymbol: {
          Symbol = 'Canceled';
          break;
        }

        case SubStudySymbol: {
          Symbol = 'SubStudy';
          break;
        }
      }
      let Action = '';
      let nSubStudyNo = null;
      switch (Symbol) {
        case '': {
          const FolderStatus = studyRowData.value.FDISPSTAT;
          if (FolderStatus !== 'Draft') {
            const oESig = {
              sComment: '',
              sUser: '',
            };
            sComment = oESig.sComment;
          }
          let aRet: any[] = [];
          if (Testcode === -101) {
            if (!aRet || aRet.length === 0) {
              return;
            }
            nSubStudyNo = aRet[0];
            Action = 'Add';
          } else {
            aRet = await PromptForNumberOfContainers(
              StabNo,
              Condition,
              Interval,
              Testcode,
            );

            if (!aRet[0]) {
              return;
            }
            nNumCont = aRet[1];
            if (matrixType.value === AddedSymbol) {
              Action = 'Add';
              row[colName] = AddedSymbol;
            } else {
              Action = 'Backup';
              row[colName] = BackupSymbol;
            }
          }

          break;
        }

        case 'Added': {
          Action = 'Delete';
          row[colName] = ' ';
          break;
        }

        case 'BackUp': {
          Action = 'Delete';
          row[colName] = ' ';
          break;
        }

        case 'Canceled': {
          Action = 'UnCancel';
          break;
        }

        case 'SubStudy': {
          // const okToContinue = await Dialogs.MessageBox(
          //   form.Resources.DeleteRelatedSubStudy,
          //   form.Resources.Question,
          //   'YESNO',
          //   'QUESTION',
          // );
          // if (okToContinue == 'NO') return;

          // Action = 'Delete';
          // sProcessClick = await ProcessClick(
          //   colName,
          //   form.Resources['Delete Item'],
          //   ' ',
          //   sender,
          // );
          return;
        }
      }

      const NeedRefres = await $createXorders_XordTaskApi({
        stabNo: StabNo,
        condition: Condition,
        interval: Interval,
        testcode: Testcode,
        action: Action,
        reason: sComment,
        mode: null,
        subStudyNo: nSubStudyNo,
        numCont: nNumCont,
        removeCondition: bRemoveCondition,
        startStudy: bStartStudy,
      });
      message.success('操作成功');
      if (NeedRefres) {
        // await loadMatrixGridData();
      }
    },
  },
);

const {
  Grid: TestListGrid,
  gridApi: testListGridApi,
  CurrentRow: testListCurrentRow,
} = useLimsGridsConfig<StabilityStudiesApi.XOrdtask>(
  useTestListColumns(),
  [],
  async () => {
    isExpand.value = false; // 折叠
    if (!studyRowData.value) {
      return [];
    }
    const data = await $dsGetProjectedTestApi({
      stabNo: studyRowData.value.STABNO,
    });
    return data;
  },
  {
    pagerConfig: {
      enabled: false,
    },
    rowGroupConfig: {
      groupFields: ['CONDITION', 'INTERVAL'],
      trigger: 'row',
    },
  },
);

const {
  Grid: ConditionQtyGrid,
  gridApi: conditionQtyGridApi,
  CurrentRow: ConditionQtyCurrentRow,
  saveRowEvent: saveRowEventConditionQty,
} = useLimsGridsConfig<StabilityStudiesApi.Stabinventor>(
  useConditionQtyColumns(),
  [],
  async () => {
    if (!studyRowData.value) {
      return [];
    }
    return await $conditionQtyApi({
      stabNo: studyRowData.value.STABNO,
    });
  },
  {
    pagerConfig: {
      enabled: false,
    },
    showFooter: true,
    params: {
      tableName: 'STABINVENTOR',
      limsControlId: 'ConditionQty_gd',
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
  },
  {},
  {},
  [
    {
      field: 'BACKUPQTY',
      aggregateFunction: 'SUM',
    },
    {
      field: 'EXTRAQTY',
      aggregateFunction: 'SUM',
    },
    {
      field: 'NUMCONT',
      aggregateFunction: 'SUM',
    },
    {
      field: 'TOTALQTY',
      aggregateFunction: 'SUM',
    },
  ],
);

watch(
  () => ConditionQtyCurrentRow.value,
  (_newVal) => {
    sumTestPerConditionGridApi.reload();
  },
);

const {
  Grid: SumTestPerConditionGrid,
  gridApi: sumTestPerConditionGridApi,
  CurrentRow: SumTestPerConditionCurrentRow,
} = useLimsGridsConfig<StabilityStudiesApi.XOrders>(
  useSumTestPerConditionColumns(),
  [],
  async () => {
    if (!ConditionQtyCurrentRow.value) {
      return [];
    }
    return await $sumPerConditionApi({
      stabNo: ConditionQtyCurrentRow.value.STABNO,
      condition: ConditionQtyCurrentRow.value.CONDITION,
    });
  },
  {
    pagerConfig: {
      enabled: false,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
  },
);

const {
  Grid: AttachmentGrid,
  gridApi: attachmentGridApi,
  CurrentRow: attachmentCurrentRow,
} = useLimsGridsConfig<StabilityStudiesApi.XAttachments>(
  useAttachmentColumns(),
  [],
  async () => {
    if (!studyRowData.value) {
      return [];
    }
    return await $getAttachmentsApi({
      stabNo: studyRowData.value.STABNO,
    });
  },
  {
    pagerConfig: {
      enabled: false,
    },
  },
);

const {
  Grid: ResultGrid,
  gridApi: resultGridApi,
  CurrentRow: resultCurrentRow,
} = useLimsGridsConfig<StabilityStudiesApi.Results>(
  useResultColumns(),
  [],
  async (params) => {
    const { currentPage, pageSize } = params.page;
    if (!studyRowData.value) {
      return [];
    }
    return await $getResultsApi({
      params: {
        oParameters: {
          aRecParams: [studyRowData.value.STABNO],
        },
      },
      currentPage,
      pageSize,
    });
  },
);

const {
  Grid: ReportGrid,
  gridApi: reportGridApi,
  CurrentRow: reportCurrentRow,
} = useLimsGridsConfig<StabilityStudiesApi.StabilityReports>(
  useReportColumns(),
  [],
  async () => {
    if (!studyRowData.value) {
      return [];
    }
    const data = await $getReportByStabnoApi({
      stabNo: studyRowData.value.STABNO,
      stepCode: formVar.value.StepCode,
    });
    return data;
  },
  {
    pagerConfig: {
      enabled: false,
    },
  },
);

const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData();
      if (data) {
        mode.value = data.mode;
        studyRowData.value = data.data;
        formVar.value = data.formVar;

        oArg.value.aDsParam = [studyRowData.value?.STABNO ?? -1];
        loadMatrixGridData();
      }
    }
  },
  showConfirmButton: false,
  showCancelButton: false,
});
const [EditCondIntervalModal, editCondIntervalModalApi] = useVbenModal({
  connectedComponent: AddStudyWizard,
  destroyOnClose: true,
});
const [IntervalTatModal, intervalTatModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: StabnoIntervalsTat,
});
const [DlgChangeDeptSrvgrpModal, dlgChangeDeptSrvgrpModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: DlgChangeDeptSrvgrp,
});
const [UpdStabInvModal, updStabInvModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: UpdStabInv,
});
const [CreatereportModal, createreportModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: SelectStabReportModel,
});
const [SelectFoltIdModal, selectFoltIdModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: CreateStabilityReport,
});
const [SelBackResultModal, selBackResultModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: SelBackRes,
});
const [GetInventoryModal, getInventoryModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: GetInventory,
});
const [LocationModal, locationModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: GetLocation,
});
async function loadMatrixGridData() {
  const row = studyRowData.value;
  if (row) {
    const stabilityArrayData = await $getStabilityArrayDataApi({
      stabNo: row.STABNO,
    });
    const testConditionsArray = stabilityArrayData[0];
    const testConditionColumn = stabilityArrayData[0].map(
      (item: string[]) => item[0],
    );
    const intervalColumns = stabilityArrayData[1];
    const captions = intervalColumns.map((item: string[]) => item[0]);
    const dataFills = stabilityArrayData[2];
    if (dataFills !== null) {
      for (const dataFill of dataFills) {
        const TestnoCondition = dataFill[0];
        const Interval = dataFill[1];
        const DueDate = dataFill[2];
        const Status = dataFill[3];
        const nSubStudyNo = dataFill[11];
        const nOrigRec = dataFill[4];
        const strOrigSts = dataFill[5];
        const dPullDate = dataFill[6];
        const dPullTo = dataFill[7];
        const dPullFrom = dataFill[8];
        const y_Pos = testConditionColumn.indexOf(TestnoCondition);
        const x_Pos = captions.indexOf(Interval);
        let TheSymbol = '';
        if (y_Pos !== -1 && x_Pos !== -1) {
          if (
            DueDate < new Date(new Date().setHours(0, 0, 0, 0)) &&
            Status === 'Started'
          ) {
            TheSymbol = LateSymbol;
          } else if (Status === 'BACKUP') {
            TheSymbol = BackupSymbol;
          } else if (Status === 'SUBSTUDY') {
            TheSymbol = nSubStudyNo + SubStudySymbol;
          } else if (
            Status === 'Started' &&
            (DueDate >= new Date(new Date().setHours(0, 0, 0, 0)) ||
              DueDate === null)
          ) {
            TheSymbol = ScheduledSymbol;
          } else
            switch (Status) {
              case 'Cancelled': {
                TheSymbol = CancelledSymbol;

                break;
              }
              case 'Draft':
              case 'Logged': {
                TheSymbol = AddedSymbol;

                break;
              }
              case 'Pulled': {
                TheSymbol =
                  dPullDate < dPullFrom || dPullDate > dPullTo
                    ? PulledOutsideSymbol
                    : PulledSymbolSymbol;

                break;
              }
              case 'Stopped': {
                TheSymbol = StoppedSymbol;

                break;
              }
              case 'TPReleased': {
                TheSymbol = TPReleasedSymbol;

                break;
              }
              default: {
                if (Status === 'Released') {
                  TheSymbol = ReleasedSymbol;
                } else if (
                  Status === '...' ||
                  Status === 'RTR' ||
                  Status === 'RTROOS'
                ) {
                  TheSymbol = ResultedSymbol;
                } else {
                  TheSymbol = AddedSymbol;
                }
              }
            }
          testConditionsArray[y_Pos][x_Pos + 4] = TheSymbol;
        }
        if (y_Pos >= 0) {
          testConditionsArray[y_Pos][30] = nOrigRec;
          testConditionsArray[y_Pos][31] = strOrigSts;
        }
      }
    }
    const columns = condIntTestMatrixGridApi.grid.columns ?? [];
    for (let j = 0; j < 27; j++) {
      const colName = `Col${j + 5}`;
      const col = columns.find((item) => item.field === colName);
      if (col) {
        if (captions.length > j) {
          col.visible = true;
          col.title = captions[j];
        } else {
          col.visible = false;
        }
      }
    }

    // 将字符串数组转换成对象数组
    const dataArray = [];
    for (const element of testConditionsArray) {
      const obj: Record<string, any> = {};
      for (const column of columns) {
        if (column.field) {
          const index = Number.parseInt(column.field.slice(3)) - 1; // 假设字段名是 "Col1", "Col2" 等
          obj[column.field] = element[index];
        }
      }
      dataArray.push(obj);
    }
    condIntTestMatrixGridApi.setGridOptions({
      columns,
      data: dataArray,
    });
  } else {
    condIntTestMatrixGridApi.setGridOptions({
      columns: useCondIntTestMatrixColumns(),
      data: [],
    });
  }
  await nextTick();
  // condIntTestMatrixGridApi.grid?.setAllRowGroupExpand(true); // 展开所有行组
}

async function validateEditLayout(
  row: XFolderApi.CondIntTestMatrix,
  column: VxeTableDefines.ColumnInfo<XFolderApi.CondIntTestMatrix>,
) {
  const oRet = {
    bValid: false, // 是否可用双击事件
    bRemoveCondition: false, // 是否移除条件
    bStartStudy: false, // 是否自动开启稳定性
  };
  if (!studyRowData.value) {
    return oRet;
  }
  if (row.isAggregate) {
    return oRet;
  }
  const folderStatus = studyRowData.value?.FDISPSTAT;
  if (
    folderStatus === 'Stopped' ||
    folderStatus === null ||
    folderStatus === 'Completed'
  ) {
    return oRet;
  }
  if (mode.value === 'Complete' || mode.value === 'View') {
    return oRet;
  }

  const colName = column.field;
  const data = row[colName];
  if (colName === 'Col3' || colName === 'Col4') {
    // 阻止点击Col3、Col4列
    return oRet;
  }
  if (!(data === AddedSymbol || data === BackupSymbol)) {
    oRet.bValid = true;
    return oRet;
  }
  const nStabno = studyRowData.value.STABNO;
  const sCondition = row.Col4;
  const bRet = await $validateEditLayoutApi({
    stabNo: nStabno,
    condition: sCondition,
  });
  if (bRet === 0) {
    // 为0，表示该条件下还有其他测试，并不是最后一个
    oRet.bValid = true;
    return oRet;
  }

  let sMsg = '';
  if (bRet === -1) {
    sMsg = $t('stability-studies.ui.delEmptyCondition');
    oRet.bRemoveCondition = true;
  } else if (bRet === -2) {
    sMsg = $t('stability-studies.ui.delEmptyConditionAndStartStudy');
    oRet.bRemoveCondition = true;
    oRet.bStartStudy = true;
  }
  const yes = await confirm($t('commons.question'), sMsg);
  if (!yes) {
    oRet.bValid = false;
    return oRet;
  }
  oRet.bValid = true;
  return oRet;
}

async function PromptForNumberOfContainers(
  StabNo: number,
  Condition: string,
  Interval: string,
  Testcode: number,
): Promise<any[]> {
  const nNumCont = 0;
  const aRet = await $checkLayoutSampleApi({
    stabNo: StabNo,
    condition: Condition,
    interval: Interval,
    testCode: Testcode,
  });
  if (aRet[0]) {
    return [true, nNumCont];
  }
  switch (aRet[1]) {
    case 'SampleIsAlreadyPulled': {
      return await AddTestToPulled();
    }
    case 'TestNotInProtocolWhenStudyWasAdded': {
      return await AddTestNotInProtocol();
    }
    default: {
      return [false, nNumCont];
    }
  }
}

async function AddTestNotInProtocol() {
  let nNumCont = 0;
  const aRet2 = await prompt({
    async beforeClose(scope) {
      if (scope.isConfirm && !scope.value) {
        alert('请输入容器数量');
        return false;
      }
      return true;
    },
    component: InputNumber,
    componentProps: {
      class: 'flex flex-col',
    },
    content: '请提供所需容器的数量。',
    icon: 'question',
    modelPropName: 'value',
  });
  if (!aRet2 || aRet2.length === 0) {
    return [false, nNumCont];
  }
  nNumCont = aRet2[0];
  return [true, nNumCont];
}

async function AddTestToPulled() {
  const nNumCont = 0;
  const options = [
    { label: $t('stability-studies.ui.new'), value: 'NEW' },
    { label: $t('stability-studies.ui.existing'), value: 'EXISTING' },
  ];
  const sSelection = await prompt({
    async beforeClose(scope) {
      if (scope.isConfirm && !scope.value) {
        alert('请选择一个选项');
        return false;
      }
      return true;
    },
    component: RadioGroup,
    componentProps: {
      options,
      class: 'flex flex-col',
    },
    content: '请选择填充方式',
    icon: 'question',
    modelPropName: 'value',
  });
  if (!sSelection || sSelection.length === 0) {
    return [false, nNumCont];
  }
  switch (sSelection[0]) {
    case 'EXISTING': {
      return [true, nNumCont];
    }
    case 'NEW': {
      return await AddTestNotInProtocol();
    }
    default: {
      return [false, nNumCont];
    }
  }
}

async function onEditMatrixList() {
  if (!studyRowData.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const status = studyRowData.value.FDISPSTAT;
  if (status !== 'Draft') {
    message.warning(
      $t('business-static-tables.studyConfiguration.cannotEditNotDraft'),
    );
    return;
  }
  const openingMode = wizardMode;
  const spCode = studyRowData.value.SP_CODE;
  const stabNo = studyRowData.value.STABNO;
  const sSite = studyRowData.value.DEPT;
  const bShowName = false;
  const bDisableConditions = false;
  editCondIntervalModalApi
    .setData({
      openingMode,
      spCode,
      stabNo,
      sSite,
      bShowName,
      bDisableConditions,
      saveMode: 'Edit',
    })
    .open();
}

async function onIntervalTat() {
  if (!studyRowData.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const status = studyRowData.value.FDISPSTAT;
  const stabNo = studyRowData.value.STABNO;
  let openingMode = 'Edit';
  if (status === 'Released') {
    openingMode = 'View';
  }
  intervalTatModalApi
    .setData({
      openingMode,
      stabNo,
    })
    .open();
}

async function showFillMatrixPrompt() {
  const nStabno = studyRowData.value?.STABNO;
  if (!nStabno) {
    return;
  }
  const status = studyRowData.value?.FDISPSTAT;
  if (status !== 'Draft') {
    message.warning(
      $t('business-static-tables.studyConfiguration.cannotEditNotDraft'),
    );
    return;
  }
  let aParams: any[] | null = null;
  const sCondition = MatrixCurrentRow.value?.Col4;

  const sAction = matrixType.value === BackupSymbol ? 'Backup' : 'Add';

  const options = [
    { label: $t('commons.all'), value: 'All' },
    { label: $t('commons.row'), value: 'Row' },
    { label: $t('commons.column'), value: 'Column' },
  ];
  prompt({
    async beforeClose(scope) {
      if (scope.isConfirm && !scope.value) {
        alert('请选择一个选项');
        return false;
      }
      return true;
    },
    component: RadioGroup,
    componentProps: {
      options,
      class: 'flex flex-col',
    },
    content: '请选择填充方式',
    icon: 'question',
    modelPropName: 'value',
  })
    .then((val) => {
      const sMode = val; // 'Row' | 'Column' | 'All' | 'Delete';
      switch (sMode) {
        case 'All': {
          aParams = [nStabno, false, sAction, sMode];
          break;
        }

        case 'Column': {
          const currentCol = condIntTestMatrixGridApi.grid.getCurrentColumn();
          if (currentCol === null) {
            message.warning($t('commons.selectOneCol'));
            return;
          }
          const sColumnName = currentCol.field;
          if (sColumnName !== 'Col3' && sColumnName !== 'Col4') {
            const sInterval = currentCol.title;
            aParams = [nStabno, false, sAction, sMode, sInterval];
          }
          break;
        }

        case 'Row': {
          if (!MatrixCurrentRow.value || MatrixCurrentRow.value.isAggregate) {
            message.warning($t('commons.selectOne'));
            return;
          }
          const nPosition = MatrixCurrentRow.value.Col4;
          const nTestcode = MatrixCurrentRow.value.Col2;
          if (nTestcode === -101) {
            message.error($t('commons.noFillSubStudy'));
            return;
          }
          aParams = [
            nStabno,
            false,
            sAction,
            sMode,
            sCondition,
            nPosition,
            nTestcode,
          ];
          break;
        }

        // case 'Delete': {
        //   sAction = 'Delete';
        //   aParams = [nStabno, false, sAction, 'All'];
        // }
      }

      if (aParams === null) {
        return;
      }
      return $scFillMatrixApi(aParams);
    })
    .then((res) => {
      if (res) {
        loadMatrixGridData();
      }
    });
}

async function editMethod() {
  if (!testListCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  if (testListCurrentRow.value.isAggregate) {
    message.warning($t('stability-studies.ui.noEditAggregate'));
    return;
  }
  dlgChangeDeptSrvgrpModalApi
    .setData({
      testCode: testListCurrentRow.value.TESTCODE,
      origrec: testListCurrentRow.value.ORIGREC,
    })
    .open();
}

async function editLocation() {
  if (!ConditionQtyCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const sCondition = ConditionQtyCurrentRow.value.CONDITION;
  updStabInvModalApi
    .setData({
      origrec: ConditionQtyCurrentRow.value.ORIGREC,
      oParams: {
        treeWhr: ` and L.CONDITION = '${sCondition}'`,
        storableOnly: true,
      },
    })
    .open();
}

async function updateCurrentLocation(values: any) {
  if (ConditionQtyCurrentRow.value && studyRowData.value) {
    const aRet = await $changeChamberLocationApi({
      stabNo: ConditionQtyCurrentRow.value.STABNO,
      matCode: studyRowData.value.MATCODE,
      condition: ConditionQtyCurrentRow.value.CONDITION,
      location: values.locationCode,
      layUser: values.fullName,
      layTime: values.layTime,
      remark: values.remark,
    });
    if (!aRet[0] && aRet[1]) {
      message.error(aRet[1]);
    }
    conditionQtyGridApi.query();
  }
}

async function EditExtQty() {
  if (!ConditionQtyCurrentRow.value || !studyRowData.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const val = await prompt({
    async beforeClose(scope) {
      if (scope.isConfirm && !scope.value) {
        alert('请输入额外量');
        return false;
      }
      return true;
    },
    component: InputNumber,
    componentProps: {
      class: 'flex flex-col',
    },
    content: '请输入额外量',
    icon: 'question',
    modelPropName: 'value',
  });
  if (val) {
    await $scUpdateExtaQtyApi({
      stabNo: studyRowData.value.STABNO,
      condition: ConditionQtyCurrentRow.value.CONDITION,
      extraQty: val,
    });
    await conditionQtyGridApi.grid.setEditRow(ConditionQtyCurrentRow.value);
    ConditionQtyCurrentRow.value.EXTRAQTY = val;
    ConditionQtyCurrentRow.value.TOTALQTY =
      val +
      ConditionQtyCurrentRow.value.BACKUPQTY +
      ConditionQtyCurrentRow.value.NUMCONT;
    saveRowEventConditionQty(ConditionQtyCurrentRow.value);
  }
}

const uploadDocuments = async (info: UploadChangeParam) => {
  if (!studyRowData.value) {
    return;
  }

  if (
    info.file &&
    info.fileList &&
    info.fileList[0] &&
    info.fileList[0].originFileObj
  ) {
    attachmentGridApi.setLoading(true);
    const tempPath = await UploadWitlabTempFile(info.fileList[0].originFileObj);
    const tempServerPath = await getServerTempFilePathByWitlabId({
      key: tempPath.AccessKey,
      fileId: tempPath.FileId,
    });
    if (!tempServerPath) {
      message.error($t('commons.uploadFailed'));
      return;
    }
    await $addXAttachmentApi({
      origrec: studyRowData.value.ORIGREC,
      stabNo: studyRowData.value.STABNO,
      fileName: info.file.name,
      serverFilePath: tempServerPath,
      catalog: 'starlims_resattachment', // 固定值
      mode: mode.value,
    });
    attachmentGridApi.query();
    attachmentGridApi.setLoading(false);
  }
};

const beforeUpload = (file: UploadFile) => {
  fileList.value = [...(fileList.value || []), file];
  return false;
};

const deleteDocuments = async function () {
  const checkRecords = attachmentGridApi.grid.getCheckboxRecords();
  if (checkRecords.length === 0) {
    message.warning($t('commons.atleaseCheckOneRow'));
    return;
  }
  const yes = await confirm(
    $t('commons.question'),
    $t('commons.deleteConfirm'),
  );
  if (!yes) {
    return;
  }
  const origrecs = checkRecords.map((row) => row.ORIGREC);
  const starDocs = checkRecords.map((item) => item.STARDOC_ID);
  await $enterpriseUtilitiesDeleteAttachmentApi({
    origrecs,
    tableName: 'XATTACHMENTS',
    starDocs,
    multiSelection: true,
  });
  message.success($t('commons.deleteSuccess'));
  attachmentGridApi.query();
};

const viewDocuments = async function () {
  if (!attachmentCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = attachmentCurrentRow.value.STARDOC_ID;
  const fileName = attachmentCurrentRow.value.FILENAM;
  downloadWitlabFile(docId, fileName);
};

const isExpand = ref(false);
const expandGroup = async function () {
  isExpand.value = !isExpand.value;
  testListGridApi.grid.setAllRowGroupExpand(isExpand.value);
};
const expandBtnText = computed(() =>
  isExpand.value
    ? $t('stability-studies.ui.collapseGroup')
    : $t('stability-studies.ui.expandGroup'),
);

const changeDeptSrvgrpSuccess = async function () {
  await testListGridApi.query();
  expandGroup();
};

const onViewReport = async function () {
  if (!reportCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = reportCurrentRow.value.STARDOC_ID;
  if (!docId) {
    return;
  }
  const fileName = reportCurrentRow.value.FILENAM;
  downloadWitlabFile(docId, fileName);
};

const onSubmitReport = async function () {
  if (!reportCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  // const nStabno = reportCurrentRow.value.STABNO;
  const origrec = reportCurrentRow.value.ORIGREC;
  const status = reportCurrentRow.value.STATUS;
  const reportType = reportCurrentRow.value.REPORTTYPE;
  drawerApi.lock();
  const auditData = await showAuditModal({
    auditData: {
      workFlowCode: 'StabilityReport',
      stepCode: formVar.value.StepCode,
      ids: [origrec],
    },
  });
  if (!auditData) {
    drawerApi.unlock();
    return;
  }
  if (
    auditData.operationCode === 'Pass' &&
    reportType === $t('stability-studies.ui.SingleReport')
  ) {
    await $regenerateStabCOAApi({
      origrec,
      status,
    });
  }
  await reportGridApi.query();
  // 判断刷新后是否还有报告，没有则关闭弹窗，刷新父页面
  const reportCnt = reportGridApi.grid.data?.length ?? 0;
  if (
    reportCnt === 0 &&
    mode.value === 'StabilityReport' &&
    formVar.value.StepCode !== 'Draft'
  ) {
    emit('refresh');
    drawerApi.close();
  } else {
    drawerApi.unlock();
  }
};

const onCreateReport = async function () {
  createreportModalApi
    .setData({
      stabNo: studyRowData.value?.STABNO,
    })
    .open();
};
const createReport = async function (values: Recordable<any>) {
  if (!studyRowData.value) {
    throw new Error('studyRowData is null');
  }
  const { reportType, timepoint, condition, reportTemplate, isEng } = values;
  const stabNo = studyRowData.value.STABNO;
  if (reportType === '1') {
    const repChk = await $chkReportApi({
      stabNo,
      timepoint,
      condition,
      isEng,
    });
    if (repChk[0]) {
      const res = await $createStabCOAApi({
        stabNo,
        reportType,
        timepoint,
        condition,
        reportTemplate,
        isEng,
        comment: '',
        isAuto: 'N',
      });
      if (res[0] === false) {
        message.error(res[1]);
      }
      reportGridApi.query();
    } else {
      switch (repChk[1]) {
        case 'Approve': {
          message.warning($t('stability-studies.ui.ApproveReportExits'));
          break;
        }
        case 'Done': {
          message.warning($t('stability-studies.ui.ReportIsDone'));
          break;
        }
        case 'Draft': {
          message.warning($t('stability-studies.ui.DraftReportExits'));
          break;
        }
        case 'Release': {
          message.warning($t('stability-studies.ui.ReleaseReportExits'));
          break;
        }
        default: {
          break;
        }
      }
    }
  } else {
    // 汇总报告
    selectFoltIdModalApi
      .setData({
        stabNo,
        planNo: studyRowData.value.PLANNO,
      })
      .open();
  }
};
const LanguageCHS = ref('中文');
const StabilityReportNm = ref('稳定性报告');
const genSumReport = async function (values: Recordable<any>) {
  if (!studyRowData.value) {
    throw new Error('studyRowData is null');
  }
  const nStabno = studyRowData.value.STABNO; // 获取当前数据的系统
  const sPlanNo = studyRowData.value.PLANNO; // 获取当前数据的方案名称
  const { isOk, arrInvFlotId, arrReferPlan, arrReferFlotId } = values;
  if (!isOk) {
    return;
  }
  reportGridApi.setLoading(true);
  // 语言: 中文
  const sReportVersion = LanguageCHS.value;
  // 报告类型: 稳定性
  const sReportType = 'STABILITY';
  const nModelCode = await $getWordTempApi({
    reportName: StabilityReportNm.value,
  });
  // 返回值：所有条件，中间以"@"连接
  const strConditions = await $getConditionsApi({
    stabNo: nStabno,
  });
  const arrConditions = strConditions.split('@');
  for (const arrCondition of arrConditions) {
    // 生成报告编号
    const strReportNo = await $getReportNoByPlanNoApi({
      planNo: sPlanNo,
    });
    const nProgramId = -1;
    const aSubmitRet = await $createReportApi({
      programId: nProgramId,
      stabNo: nStabno,
      planNo: sPlanNo,
      reportNo: strReportNo,
      condition: arrCondition,
      invFlotId: arrInvFlotId,
      referPlan: arrReferPlan,
      referFlotId: arrReferFlotId,
      reportVersion: sReportVersion,
      modelCode: nModelCode,
      generateType: 'One',
      reportType: sReportType,
    });
    if (!aSubmitRet[0]) {
      message.error(aSubmitRet[1]);
      return;
    }
  }
  reportGridApi.setLoading(false);
  reportGridApi.query();
};

const onDeleteReport = async function () {
  const checkRows = reportGridApi.grid.getCheckboxRecords();
  if (!checkRows || checkRows.length === 0) {
    message.warning($t('commons.atleaseCheckOneRow'));
    return;
  }
  const origrecs = checkRows.map((item) => item.ORIGREC);
  const yes = await confirm(
    $t('commons.deleteConfirm'),
    $t('commons.question'),
  );
  if (!yes) {
    return;
  }
  const res = await $delStabilityReportApi({
    origrecs,
  });
  if (res[0] === false) {
    message.error(res[1]);
    return;
  }
  reportGridApi.query();
};

const sumitBtnText = computed(() => {
  return formVar.value.StepCode === 'Draft'
    ? $t('stability-studies.ui.submitReport')
    : $t('stability-studies.ui.releaseReport');
});

const handleRetireStabReport = async () => {
  const checkRows = reportGridApi.grid.getCheckboxRecords();
  if (!checkRows || checkRows.length === 0) {
    message.warning($t('commons.atleaseCheckOneRow'));
    return;
  }
  const yes = await confirm(
    $t('stability-studies.ui.retireReportConfirm'),
    $t('commons.question'),
  );
  if (!yes) {
    return;
  }
  const esignData = await esignBegin({
    eventCode: 'RetireStabReport',
  });
  if (!esignData) {
    return;
  }
  const aReportNo = checkRows.map((item) => item.REPORTNO);
  const sComment = esignData.comment;
  const aRet = await $retireStabReportApi({
    aReportNo,
    sComment,
  });
  if (!aRet[0]) {
    message.error(aRet[1]);
  }
  reportGridApi.query();
};

const handleSelBackResultModal = async () => {
  const stabNo = studyRowData.value?.STABNO;
  if (!stabNo) {
    return;
  }
  selBackResultModalApi.setData({ stabNo }).open();
};
async function backResult(values: Recordable<any>) {
  const nStabNo = studyRowData.value?.STABNO;
  if (!nStabNo) {
    return;
  }
  const esignData = await esignBegin({
    eventCode: 'BackStabilityResults',
  });
  if (!esignData) {
    return;
  }
  const { interval, testCode } = values;
  const res = await $stabRetReusultsApi({
    stabNo: nStabNo,
    interval,
    testCode,
    comment: esignData.comment,
  });
  if (res[0] === false) {
    message.error(res[1]);
    return;
  }
  message.success($t('commons.success'));
}

async function handleReturnChamber() {
  ReturnInChamberNotUseInvItems();
}

async function ReturnInChamberNotUseInvItems() {
  if (!SumTestPerConditionCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nOrigrec = SumTestPerConditionCurrentRow.value.ORIGREC;
  if (!nOrigrec) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nContPulled = SumTestPerConditionCurrentRow.value.CONTPULLED;
  if (nContPulled === 0) {
    message.warning($t('stability-studies.ui.notPulled'));
    return;
  }
  const bValidate = await ValidateReturn(nOrigrec);
  if (!bValidate) {
    return;
  }
  const nNumContToReturn = await prompt({
    async beforeClose(scope) {
      if (scope.isConfirm) {
        if (!scope.value || scope.value <= 0) {
          alert($t('stability-studies.ui.reqVal'));
          return false;
        }
        if (scope.value > nContPulled) {
          alert($t('stability-studies.ui.contToReturnGreaterContPulled'));
          return false;
        }
      }
      return true;
    },
    component: InputNumber,
    componentProps: {
      class: 'flex flex-col',
    },
    content: $t('stability-studies.ui.contToReturn'),
    icon: 'question',
    modelPropName: 'value',
  });
  if (!nNumContToReturn) {
    return;
  }
  const esignData = await esignBegin({
    eventCode: 'ReturnInChamber',
  });
  if (!esignData) {
    return;
  }
  const bRet = await $returnInChamberApi({
    stabilityUseInventoryItems: false,
    nOrigrec,
    nNumContToReturn,
    sListInventoryId: null,
    sComment: esignData.comment,
  });
  if (bRet) {
    conditionQtyGridApi.query();
    sumTestPerConditionGridApi.query();
  }
}

async function ValidateReturn(nOrigrec: number) {
  const aValidate = await $validateReturnApi({ nOrigrec });
  if (!aValidate[0]) {
    const sElapseTime = aValidate[1].toString();
    const sMsg = $t('stability-studies.ui.yesNoReturnInChamber', [sElapseTime]);
    const yes = await confirm(sMsg, $t('commons.question'));
    return yes;
  }
  return true;
}

async function handlePullContainer() {
  if (!SumTestPerConditionCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nXordno = SumTestPerConditionCurrentRow.value.XORDNO;
  const aRet = await $pullLeftoverContainersApi({ nXordno });
  if (!aRet[0]) {
    message.warning(aRet[1]);
    return;
  }
  conditionQtyGridApi.query();
  sumTestPerConditionGridApi.query();
}

async function handleExtra() {
  if (
    !studyRowData.value ||
    !ConditionQtyCurrentRow.value ||
    !SumTestPerConditionCurrentRow.value
  ) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nExtra = ConditionQtyCurrentRow.value.EXTRAQTY;
  const ret = await prompt({
    async beforeClose(scope) {
      if (scope.isConfirm) {
        if (!scope.value || scope.value <= 0) {
          alert($t('stability-studies.ui.reqVal'));
          return false;
        }
        if (nExtra < scope.value) {
          alert($t('stability-studies.ui.extraExceed', [nExtra]));
          return false;
        }
      }
      return true;
    },
    component: InputNumber,
    componentProps: {
      class: 'flex flex-col',
    },
    content: $t('stability-studies.ui.extra'),
    icon: 'question',
    modelPropName: 'value',
  });
  if (!ret) {
    return;
  }
  const nStabno = studyRowData.value.STABNO;
  const nCondition = ConditionQtyCurrentRow.value.CONDITION;
  const nInterval = SumTestPerConditionCurrentRow.value.INTERVAL;
  const nQty = ret;
  const esignData = await esignBegin({
    eventCode: 'ExtraPullSpamling',
  });
  if (!esignData) {
    return;
  }
  await $updExtraQtyApi({
    nStabno,
    nCondition,
    nInterval,
    nQty,
    sComment: esignData.comment,
  });
  conditionQtyGridApi.query();
  sumTestPerConditionGridApi.query();
}

async function handleInventory() {
  if (
    !studyRowData.value ||
    !ConditionQtyCurrentRow.value ||
    !SumTestPerConditionCurrentRow.value
  ) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nExtra = ConditionQtyCurrentRow.value.EXTRAQTY;
  getInventoryModalApi.setData({ qty: nExtra }).open();
}

async function updateQty(values: Recordable<any>) {
  if (
    !studyRowData.value ||
    !ConditionQtyCurrentRow.value ||
    !SumTestPerConditionCurrentRow.value
  ) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const nExtra = ConditionQtyCurrentRow.value.EXTRAQTY;
  const nStabno = studyRowData.value.STABNO;
  const nCondition = ConditionQtyCurrentRow.value.CONDITION;
  const nInterval = SumTestPerConditionCurrentRow.value.INTERVAL;
  const { nQty, nCOMMENT, isOk } = values;
  if (!isOk) {
    return;
  }
  const esignData = await esignBegin({
    eventCode: 'ExtraPullSpamling',
  });
  if (!esignData) {
    return;
  }
  await $UDInventoryApi([
    nStabno,
    nCondition,
    nInterval,
    nQty,
    nExtra,
    nCOMMENT,
    esignData.comment,
  ]);
  conditionQtyGridApi.query();
  sumTestPerConditionGridApi.query();
}

async function onSetLocation() {
  if (
    !ConditionQtyCurrentRow.value ||
    !studyRowData.value ||
    !SumTestPerConditionCurrentRow.value
  ) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const stabNo = studyRowData.value.STABNO;
  const condition = ConditionQtyCurrentRow.value.CONDITION;
  const longCode = ConditionQtyCurrentRow.value.LONGCODE;
  const interval = SumTestPerConditionCurrentRow.value.INTERVAL;
  locationModalApi
    .setData({
      location: longCode,
      updateLocation: async (modelValue: any) => {
        await $updLocation({
          stabNo,
          condition,
          interval,
          location: modelValue.location,
          longCode,
          nComment: modelValue.comment,
          comment: '',
        });
        conditionQtyGridApi.query();
      },
    })
    .open();
}

// 复测
const [RetestFcFormModal, retestFcFormModalApi] = useVbenModal({
  connectedComponent: RetestRequestFc,
  destroyOnClose: true,
});
async function onConfirmatory() {
  const aSamplesAndTests = [];
  const aStabSampTests: any[] = [];
  const checkRows = resultGridApi.grid.getCheckboxRecords();
  if (!checkRows || checkRows.length === 0) {
    message.warning($t('commons.atleaseCheckOneRow'));
    return;
  }
  for (const checkRow of checkRows) {
    aSamplesAndTests.push([checkRow.ORDNO, checkRow.TESTCODE]);
    aStabSampTests.push([checkRow.XORDNO, checkRow.TESTCODE]);
  }
  retestFcFormModalApi
    .setData({
      SamplesAndTests: aSamplesAndTests,
      afterSuccess: async () => {
        await $updateStabOrdersTestsApi(aStabSampTests);
      },
    })
    .open();
}
// 查看复试/复测
const [ViewRetestFormModal, viewRetestFormModalApi] = useVbenModal({
  connectedComponent: ViewRetestForm,
});
async function onViewConfirmatory() {
  if (!resultCurrentRow.value) {
    return;
  }

  let sRetestMode = 'RW';

  const sOrigrec = resultCurrentRow.value.ORIGREC;
  const sApprsts = resultCurrentRow.value.APPRSTS;

  if (
    sApprsts === 'Released' ||
    sApprsts === 'Rejected' ||
    mode.value === 'View'
  ) {
    sRetestMode = 'RO';
  }

  viewRetestFormModalApi
    .setData({
      ORIGREC: sOrigrec,
      RETESTMODE: sRetestMode,
    })
    .open();
}

// 查看方法
const [ViewMethodFormModal, viewMethodFormModalApi] = useVbenModal({
  connectedComponent: MethodVersionInfo,
  destroyOnClose: true,
});
async function onViewMethod() {
  if (!resultCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  viewMethodFormModalApi
    .setData({
      ordno: resultCurrentRow.value.ORDNO,
      testCode: resultCurrentRow.value.TESTCODE,
    })
    .open();
}

// 编辑分析项btnEditAnalytesList
const [EditAnalyteListModal, editAnalyteListModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: EditAnalytesList,
});
async function onEditAnalytesList() {
  if (!resultCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const ordno = resultCurrentRow.value.ORDNO;
  const testCode = resultCurrentRow.value.TESTCODE;
  editAnalyteListModalApi
    .setData({
      ordno,
      testCode,
      bAnalytesRequired: true,
    })
    .open();
}
async function updateAnalytesList(values: Recordable<any>) {
  if (!resultCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const ordno = resultCurrentRow.value.ORDNO;
  const testCode = resultCurrentRow.value.TESTCODE;
  const { analytes } = values;
  await $changeAnalytesListApi({
    ordno,
    testCode,
    analytes,
  });
  resultGridApi.query();
}

// 同步结果
const [AssignZeroModal, assignZeroModalApi] = useVbenModal({
  connectedComponent: AssignZeroInterval,
  destroyOnClose: true,
});
async function onSyncT0() {
  if (!studyRowData.value) {
    return;
  }
  const stabno = studyRowData.value.STABNO;
  const aValidate = await $syncT0Results_ValidateApi({
    nStabno: stabno,
  });
  switch (aValidate[0]) {
    case -94: {
      message.warning($t('stability-studies.ui.t0samplesApproved'));
      return;
    }
    case -93: {
      message.warning($t('stability-studies.ui.noResultsT0SyncT0'));
      return;
    }
    case -92: {
      message.warning($t('stability-studies.ui.noPackBatchForumulaBatch'));
      return;
    }
    case -91: {
      message.warning($t('stability-studies.ui.noAssignedTestsT0SyncT0'));
      return;
    }
    case -90: {
      message.warning($t('stability-studies.ui.noZeroInterval'));
      return;
    }
  }
  const aTests = aValidate[1];
  const aBatchIds = aValidate[2];
  assignZeroModalApi
    .setData({
      studyTest: aTests,
      openingMode: 'SYNC',
      stabNo: stabno,
      batchList: aBatchIds.join(','),
      confirmCallback: async ({
        nBatchId,
      }: {
        aSelection: any[];
        nBatchId: number;
        nStabno: number;
        sBatchno: string;
        sFormPack: string;
      }) => {
        const esignData = await esignBegin({
          eventCode: 'SynchronizeT0Results',
        });
        if (!esignData) {
          return;
        }

        const aRet = await $syncT0ResultsApi({
          nBatchId,
          nStabno: stabno,
          sComment: esignData.comment,
        });
        if (!aRet[0]) {
          message.warning(aRet[1]);
          return;
        }

        resultGridApi.reload();
      },
    })
    .open();
}
const [AttachFormModal, attachFormModalApi] = useVbenModal({
  connectedComponent: AttachForm,
  destroyOnClose: true,
});
async function onViewAttach() {
  if (!resultCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const sSampleStatus = resultCurrentRow.value.TSA;
  const sOrigec = resultCurrentRow.value.ORIGEC;
  const sReadonly = sSampleStatus === 'Released' || mode.value === 'View';

  attachFormModalApi
    .setData({
      TABLE: 'RESULTS',
      SID: sOrigec,
      READONLY: sReadonly,
    })
    .open();
}
const [HistoryModal, historyModalApi] = useVbenModal({
  connectedComponent: TraceabilityView,
  destroyOnClose: true,
  fullscreen: true,
});
async function onViewHistory() {
  // await csOpenTraceability(Results_gd, "I", null); // TODO
  if (!resultCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const sOrdno = resultCurrentRow.value.ORDNO;
  const sWhr =
    ` (	ORDTASK.ORDNO in (Select o2.ORDNO ` +
    `						from #ORDERS o2 ` +
    `						where o2.ORDGROUP = '${sOrdno}' ` +
    `						) ` +
    `	or ` +
    `	ORDTASK.ORDNO = '${sOrdno}' ` +
    ` ) `;
  const sGroupBy = 'O';
  historyModalApi
    .setData({
      sWhr,
      sGroupBy,
    })
    .open();
}

async function onReopenSample() {
  ReOpenSample();
}

async function ReOpenSample() {
  const aSel = resultGridApi.grid.getCheckboxRecords();
  if (aSel.length <= 0) {
    message.warning($t('commons.atleaseCheckOneRow'));
    return;
  }
  const sAuditEvent = 'ReOpenOrder';

  const aSelOrdno = aSel.map((item) => item.ORDNO);
  const aSelRelStatus = aSel.map((item) => item.O_APPRDISP);
  const aOrdno = [];
  for (const [i, element] of aSelOrdno.entries()) {
    if (aSelRelStatus[i] !== null && aSelRelStatus[i] !== '...')
      aOrdno.push(element);
  }
  if (aOrdno.length === 0) {
    message.warning($t('stability-studies.ui.noOrderToReopen'));
    return;
  }
  const esigData = await esignBegin({
    eventCode: sAuditEvent,
  });
  const bRet = await $reOpenOrderApi({
    aOrdno,
    sComment: esigData.comment,
    sAuditEvent,
  });
  if (!bRet) return;
  resultGridApi.query();
}
</script>

<template>
  <Drawer class="w-full max-w-[1200px]" :title="$t('commons.detail')">
    <EditCondIntervalModal class="w-[800px]" @success="loadMatrixGridData()" />
    <IntervalTatModal />
    <DlgChangeDeptSrvgrpModal @success="changeDeptSrvgrpSuccess" />
    <UpdStabInvModal @success="updateCurrentLocation($event)" />
    <CreatereportModal @success="createReport($event)" />
    <SelectFoltIdModal @success="genSumReport($event)" />
    <SelBackResultModal @success="backResult($event)" />
    <GetInventoryModal @update-qty="updateQty($event)" />
    <LocationModal class="w-[500px]" />
    <ViewRetestFormModal />
    <ViewMethodFormModal />
    <EditAnalyteListModal @success="updateAnalytesList($event)" />
    <AssignZeroModal />
    <RetestFcFormModal @success="resultGridApi?.query()" />
    <AttachFormModal />
    <HistoryModal />
    <Page auto-content-height>
      <Tabs v-model:active-key="activeKey" class="h-full">
        <TabPane key="1" :tab="$t('stability-studies.ui.studyConfig')">
          <CondIntTestMatrixGrid class="h-full">
            <template #toolbar-actions>
              <Space :size="[4, 0]" wrap>
                <Button
                  type="default"
                  @click="onEditMatrixList"
                  v-if="buttonConfigs.Edit_btn.visible()"
                  :disabled="!buttonConfigs.Edit_btn.enabled()"
                >
                  {{ $t('ui.actionTitle.edit') }}
                </Button>
                <Button
                  type="default"
                  @click="onIntervalTat"
                  v-if="buttonConfigs.btnIntervalsTat.visible()"
                  :disabled="!buttonConfigs.btnIntervalsTat.enabled()"
                >
                  {{ $t('stability-studies.ui.interval') }}
                </Button>
                <Button
                  type="default"
                  @click="showFillMatrixPrompt"
                  v-if="buttonConfigs.btnFill.visible()"
                  :disabled="!buttonConfigs.btnFill.enabled()"
                >
                  {{ $t('stability-studies.ui.fillMatrix') }}
                </Button>
                <RadioGroup v-model:value="matrixType" size="small">
                  <Radio :value="AddedSymbol">
                    {{ $t('commons.test') }}
                  </Radio>
                  <Radio :value="BackupSymbol">
                    {{ $t('commons.backup') }}
                  </Radio>
                </RadioGroup>
              </Space>
            </template>
          </CondIntTestMatrixGrid>
        </TabPane>
        <TabPane key="2" :tab="$t('stability-studies.ui.testList')">
          <TestListGrid>
            <template #toolbar-actions>
              <Space :size="[4, 0]" wrap>
                <Button
                  type="default"
                  @click="editMethod()"
                  v-if="buttonConfigs.btnSetMethod.visible()"
                  :disabled="!buttonConfigs.btnSetMethod.enabled()"
                >
                  {{ $t('stability-studies.ui.editMethod') }}
                </Button>
                <Button type="default" @click="expandGroup()">
                  {{ expandBtnText }}
                </Button>
              </Space>
            </template>
          </TestListGrid>
        </TabPane>
        <TabPane key="3" :tab="$t('stability-studies.ui.chambers')">
          <ResizablePanelGroup
            direction="vertical"
            class-name="max-w-md rounded-lg border md:min-w-[450px]"
          >
            <ResizablePanel :default-size="30">
              <div class="h-full w-full">
                <ConditionQtyGrid>
                  <template #toolbar-actions>
                    <Space :size="[4, 0]" wrap>
                      <Button
                        type="default"
                        @click="editLocation()"
                        v-if="buttonConfigs.btnSetLongCode.visible()"
                      >
                        {{ $t('stability-studies.ui.storeLocation') }}
                      </Button>
                      <Button
                        type="default"
                        @click="EditExtQty()"
                        v-if="buttonConfigs.btnSetExtraQty.visible()"
                      >
                        {{ $t('stability-studies.ui.editExtQty') }}
                      </Button>
                    </Space>
                  </template>
                </ConditionQtyGrid>
              </div>
            </ResizablePanel>
            <ResizableHandle with-handle />
            <ResizablePanel :default-size="70">
              <div class="h-full w-full">
                <SumTestPerConditionGrid>
                  <template #toolbar-actions>
                    <Space :size="[4, 0]" wrap>
                      <Button
                        type="default"
                        @click="handleReturnChamber"
                        v-if="buttonConfigs.btnReturnChamber.visible()"
                        :disabled="!buttonConfigs.btnReturnChamber.enabled()"
                      >
                        {{ $t('stability-studies.ui.returnChamber') }}
                      </Button>
                      <Button
                        type="default"
                        @click="handlePullContainer"
                        v-if="buttonConfigs.btnPullContainer.visible()"
                        :disabled="!buttonConfigs.btnPullContainer.enabled()"
                      >
                        {{ $t('stability-studies.ui.pullContainer') }}
                      </Button>
                      <Button
                        type="default"
                        @click="handleExtra"
                        v-if="buttonConfigs.btnExtra.visible()"
                        :disabled="!buttonConfigs.btnExtra.enabled()"
                      >
                        {{ $t('stability-studies.ui.extra') }}
                      </Button>
                      <Button
                        type="default"
                        @click="handleInventory"
                        v-if="buttonConfigs.btnInventory.visible()"
                        :disabled="!buttonConfigs.btnInventory.enabled()"
                      >
                        {{ $t('stability-studies.ui.updateInventory') }}
                      </Button>
                      <Button
                        type="default"
                        @click="onSetLocation"
                        v-if="buttonConfigs.btnlocation.visible()"
                        :disabled="!buttonConfigs.btnlocation.enabled()"
                      >
                        {{ $t('stability-studies.ui.setLocation') }}
                      </Button>
                    </Space>
                  </template>
                </SumTestPerConditionGrid>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </TabPane>
        <TabPane
          key="4"
          :tab="$t('business-static-tables.studyConfiguration.signatures')"
        >
          <Signatures v-model="oArg" ref="signaturesRef" />
        </TabPane>
        <TabPane key="5" :tab="$t('commons.attachments')">
          <AttachmentGrid class="h-full">
            <template #toolbar-actions>
              <Space :size="[4, 0]" wrap>
                <Upload
                  v-model:file-list="fileList"
                  name="file"
                  :show-upload-list="false"
                  :headers="headers"
                  :max-count="1"
                  @change="uploadDocuments"
                  :before-upload="beforeUpload"
                >
                  <Button type="primary">
                    {{ $t('commons.uploadFile') }}
                  </Button>
                </Upload>
                <Button type="primary" danger @click="deleteDocuments">
                  {{ $t('ui.actionTitle.delete') }}
                </Button>
                <Button type="default" @click="viewDocuments">
                  {{ $t('commons.viewFile') }}
                </Button>
              </Space>
            </template>
          </AttachmentGrid>
        </TabPane>
        <TabPane
          key="6"
          :tab="$t('stability-studies.ui.result')"
          v-if="
            mode !== 'LifeCycle' &&
            mode !== 'Review Study' &&
            mode !== 'Sampling Study' &&
            mode !== 'Create'
          "
        >
          <ResultGrid>
            <template #toolbar-actions>
              <Space :size="[4, 0]" wrap>
                <Button type="default">
                  {{ $t('stability-studies.ui.viewGraph') }}
                </Button>
                <Button type="default">
                  {{ $t('stability-studies.ui.viewEln') }}
                </Button>
                <Button
                  type="default"
                  @click="onConfirmatory"
                  v-if="buttonConfigs.btnConfirmatory.visible()"
                >
                  {{ $t('stability-studies.ui.confirmatory') }}
                </Button>
                <!-- btnViewConfirmatory -->
                <Button
                  type="default"
                  @click="onViewConfirmatory"
                  :disabled="!buttonConfigs.btnViewConfirmatory.enabled()"
                >
                  {{ $t('stability-studies.ui.viewConfirmatory') }}
                </Button>
                <Button type="default" @click="onViewMethod">
                  {{ $t('stability-studies.ui.viewMethod') }}
                </Button>
                <!-- btnEditAnalytesList -->
                <Button
                  type="default"
                  @click="onEditAnalytesList"
                  v-if="buttonConfigs.btnEditAnalytesList.visible()"
                  :disabled="!buttonConfigs.btnEditAnalytesList.enabled()"
                >
                  {{ $t('stability-studies.ui.editAnalytesList') }}
                </Button>
                <!-- btnSyncT0 -->
                <Button
                  type="default"
                  @click="onSyncT0"
                  v-if="buttonConfigs.btnSyncT0.visible()"
                  :disabled="!buttonConfigs.btnSyncT0.enabled()"
                >
                  {{ $t('stability-studies.ui.syncT0') }}
                </Button>
                <Button @click="onViewAttach">
                  {{ $t('stability-studies.ui.viewAttach') }}
                </Button>
                <Button @click="onViewHistory">
                  {{ $t('stability-studies.ui.viewHistory') }}
                </Button>
                <Button
                  type="default"
                  @click="onReopenSample"
                  v-if="buttonConfigs.btnReopenSample.visible()"
                  :disabled="!buttonConfigs.btnReopenSample.enabled()"
                >
                  {{ $t('stability-studies.ui.reopenSample') }}
                </Button>
              </Space>
            </template>
          </ResultGrid>
        </TabPane>
        <TabPane
          key="7"
          :tab="$t('stability-studies.ui.report')"
          v-if="
            mode === 'Edit' || mode === 'View' || mode === 'StabilityReport'
          "
        >
          <ReportGrid>
            <template #toolbar-actions>
              <Space :size="[4, 0]" wrap>
                <Button
                  type="primary"
                  @click="onCreateReport"
                  v-if="formVar.StepCode === 'Draft'"
                >
                  {{ $t('stability-studies.ui.createReport') }}
                </Button>
                <Button
                  type="default"
                  @click="onSubmitReport"
                  v-if="buttonConfigs.btnSubmitStabReport.visible()"
                >
                  {{ sumitBtnText }}
                </Button>
                <Button type="default" @click="onViewReport">
                  {{ $t('stability-studies.ui.viewReport') }}
                </Button>
                <Button
                  type="primary"
                  @click="onDeleteReport"
                  danger
                  v-if="buttonConfigs.btnDelStabReport.visible()"
                >
                  {{ $t('stability-studies.ui.deleteReport') }}
                </Button>
                <Button
                  @click="handleRetireStabReport"
                  v-if="buttonConfigs.btnRetireStabReport.visible()"
                  :disabled="!buttonConfigs.btnRetireStabReport.enabled()"
                >
                  {{ $t('stability-studies.ui.retireStabReport') }}
                </Button>
                <Button
                  @click="handleSelBackResultModal"
                  v-if="buttonConfigs.btnBackRes.visible()"
                >
                  {{ $t('stability-studies.ui.backResult') }}
                </Button>
              </Space>
            </template>
          </ReportGrid>
        </TabPane>
      </Tabs>
    </Page>
  </Drawer>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
