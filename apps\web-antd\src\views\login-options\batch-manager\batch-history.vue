<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';
import dayjs from 'dayjs';

import { downloadWitlabFile } from '#/api/core/witlab';
import {
  buildDataSetApi,
  dgdDocsApi,
  dgdResultsApi,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import AttachForm from '#/views/common-apps/eln-running/attachment-chrom.vue';

import {
  useBatchHistoryColumns,
  useBatchHistoryDocColumns,
  useBatchHistoryDocFilterSchema,
  useBatchHistoryFilterSchema,
} from './batch-manager-data';

// 定义组件事件
// const emit = defineEmits(['success']);
const activeKey = ref('tpResults');
const sBatchId = ref<number>(0);
const sGuid = ref<string>('');
const sFolderNo = ref<string>('');

const colums = useBatchHistoryColumns();
const filterSchema = useBatchHistoryFilterSchema();

const queryData = async () => {
  return dgdResultsApi(sGuid.value);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid: ResultsGrid,
  gridApi: resultsGridApi,
  CurrentRow: resultsCurrentRow,
} = useLimsGridsConfig<BatcheManagerApi.ApproveResults>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  resultsGridApi.query(); // 主动触发一次查询
}

const testColums = useBatchHistoryDocColumns();
const testFilterSchema = useBatchHistoryDocFilterSchema();

const testQueryData = async () => {
  return await dgdDocsApi(sFolderNo.value);
};
const testGirdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid: DocsGrid,
  gridApi: docGridApi,
  CurrentRow: docCurrentRow,
} = useLimsGridsConfig<BatcheManagerApi.BatchHistoryDoc>(
  testColums,
  testFilterSchema,
  testQueryData,
  testGirdOption,
);

function onRetestFyRefresh() {
  docGridApi.query(); // 主动触发一次查询
}

const [AttachFormModal, attachFormModalApi] = useVbenModal({
  connectedComponent: AttachForm,
});

async function onViewAttach() {
  if (resultsCurrentRow.value === null) return;

  attachFormModalApi
    .setData({
      TABLE: 'RESULTS',
      SID: resultsCurrentRow.value.RESULTSORIGREC,
      READONLY: true,
    })
    .open();
}

async function onViewDocuments() {
  if (!docCurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const docId = docCurrentRow.value.STARDOC_ID;
  downloadWitlabFile(docId, '');
}

// 获取Modal实例
const [Modal, modalApi] = useVbenModal({
  title: '追溯',
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any>>();
      sBatchId.value = data.BATCHID;
      const batchParams = await buildDataSetApi(sBatchId.value);
      sGuid.value = batchParams[0];
      const batchHeaders = batchParams[1];
      sFolderNo.value = batchHeaders[0];
      formApi.setValues({
        BATCHNO: batchHeaders[1],
        PRODUCT: batchHeaders[6],
        STARTDATE: batchHeaders[5] ? dayjs(batchHeaders[5]) : null,
        ENDDATE: batchHeaders[4] ? dayjs(batchHeaders[4]) : null,
      });
      formApi.updateSchema([
        {
          fieldName: 'BATCHNO',
          componentProps: {
            disabled: true,
          },
        },
        {
          fieldName: 'PRODUCT',
          componentProps: {
            disabled: true,
          },
        },
        {
          fieldName: 'STARTDATE',
          componentProps: {
            disabled: true,
          },
        },
        {
          fieldName: 'ENDDATE',
          componentProps: {
            disabled: true,
          },
        },
      ]);
      onRefresh();
      onRetestFyRefresh();
      activeKey.value = 'tpResults';
    }
  },
});

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  // handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      fieldName: 'BATCHNO',
      label: $t('login-options.batchHistory.batchNo'),
    },
    {
      component: 'Input',
      fieldName: 'PRODUCT',
      label: $t('login-options.batchHistory.product'),
    },
    {
      component: 'DatePicker',
      fieldName: 'STARTDATE',
      label: $t('login-options.batchHistory.startDate'),
    },
    {
      component: 'DatePicker',
      fieldName: 'ENDDATE',
      label: $t('login-options.batchHistory.endDate'),
    },
  ],
  wrapperClass: 'grid-cols-4',
  showDefaultActions: false,
});
</script>

<template>
  <Modal class="h-[700px] w-[1300px]">
    <AttachFormModal />
    <Tabs v-model:active-key="activeKey" class="h-full">
      <TabPane key="tpResults" tab="结果">
        <Form />
        <ResultsGrid class="h-full">
          <template #toolbar-actions>
            <Space>
              <Button type="default" @click="onViewAttach">
                {{ $t('login-options.batchHistory.viewAttachment') }}
              </Button>
            </Space>
          </template>
        </ResultsGrid>
      </TabPane>
      <TabPane key="tpRunsDoc" tab="分析批记录">
        <DocsGrid class="h-full">
          <template #toolbar-actions>
            <Space>
              <Button type="default" @click="onViewDocuments">
                {{ $t('login-options.batchHistory.view') }}
              </Button>
            </Space>
          </template>
        </DocsGrid>
      </TabPane>
    </Tabs>
  </Modal>
</template>
