<template>
  <VbenModal
    v-bind="$attrs"
    :title="modalTitle"
    :width="800"
    :loading="loading"
    @ok="handleOk"
  >
    <div v-if="notification" class="notification-detail">
      <!-- 基本信息 -->
      <div class="notification-detail__section">
        <h3 class="notification-detail__section-title">基本信息</h3>
        <div class="notification-detail__grid">
          <div class="notification-detail__item">
            <label>标题：</label>
            <span>{{ notification.content.title }}</span>
          </div>
          <div class="notification-detail__item">
            <label>类型：</label>
            <Tag :color="typeConfig.color">{{ typeConfig.name }}</Tag>
          </div>
          <div class="notification-detail__item">
            <label>分类：</label>
            <Tag :color="categoryConfig.color">{{ categoryConfig.name }}</Tag>
          </div>
          <div class="notification-detail__item">
            <label>优先级：</label>
            <Tag :color="priorityConfig.color">{{ priorityConfig.name }}</Tag>
          </div>
          <div class="notification-detail__item">
            <label>状态：</label>
            <Badge
              :status="notification.status === 0 ? 'error' : 'default'"
              :text="statusConfig.name"
            />
          </div>
          <div class="notification-detail__item">
            <label>创建时间：</label>
            <span>{{ formatTime(notification.createdAt) }}</span>
          </div>
          <div v-if="notification.readAt" class="notification-detail__item">
            <label>已读时间：</label>
            <span>{{ formatTime(notification.readAt) }}</span>
          </div>
          <div v-if="notification.expiresAt" class="notification-detail__item">
            <label>过期时间：</label>
            <span>{{ formatTime(notification.expiresAt) }}</span>
          </div>
        </div>
      </div>

      <!-- 内容信息 -->
      <div class="notification-detail__section">
        <h3 class="notification-detail__section-title">内容信息</h3>
        <div class="notification-detail__content">
          <div class="notification-detail__item">
            <label>消息内容：</label>
            <div class="notification-detail__message">
              {{ notification.content.message }}
            </div>
          </div>
          <div v-if="notification.content.details" class="notification-detail__item">
            <label>详细信息：</label>
            <div class="notification-detail__details">
              <pre>{{ notification.content.details }}</pre>
            </div>
          </div>
          <div v-if="notification.content.data" class="notification-detail__item">
            <label>附加数据：</label>
            <div class="notification-detail__data">
              <JsonViewer
                :value="parsedData"
                :expand-depth="2"
                copyable
                boxed
                sort
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 接收人信息 -->
      <div v-if="notification.recipients?.length > 0" class="notification-detail__section">
        <h3 class="notification-detail__section-title">
          接收人信息 ({{ notification.recipients.length }})
        </h3>
        <div class="notification-detail__recipients">
          <Tag
            v-for="recipient in notification.recipients"
            :key="recipient"
            class="mb-1"
          >
            {{ recipient }}
          </Tag>
        </div>
      </div>

      <!-- 来源信息 -->
      <div v-if="notification.sourceType || notification.sourceId" class="notification-detail__section">
        <h3 class="notification-detail__section-title">来源信息</h3>
        <div class="notification-detail__grid">
          <div v-if="notification.sourceType" class="notification-detail__item">
            <label>来源类型：</label>
            <span>{{ notification.sourceType }}</span>
          </div>
          <div v-if="notification.sourceId" class="notification-detail__item">
            <label>来源ID：</label>
            <span>{{ notification.sourceId }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="notification-detail__actions">
        <Space>
          <Button
            v-if="notification.status === 0"
            type="primary"
            @click="handleMarkRead"
          >
            <CheckCircle class="w-4 h-4 mr-1" />
            标记已读
          </Button>
          <Button
            danger
            @click="handleDelete"
          >
            <Trash2 class="w-4 h-4 mr-1" />
            删除通知
          </Button>
        </Space>
      </div>
    </div>
  </VbenModal>
</template>

<script setup lang="ts">
import type { Notification } from '#/types/notification';

import { computed, ref } from 'vue';

import { VbenModal } from '@vben/common-ui';

import { Badge, Button, message, Space, Tag } from 'ant-design-vue';
import { CheckCircle, Trash2 } from 'lucide-vue-next';
import JsonViewer from 'vue-json-pretty';

import { notificationApi } from '#/api/notification';
import {
  getNotificationCategoryConfig,
  getNotificationPriorityConfig,
  getNotificationStatusConfig,
  getNotificationTypeConfig,
} from '#/config/notification';

// Props
interface Props {
  notification?: Notification;
}

const props = defineProps<Props>();

// 事件
const emit = defineEmits<{
  ok: [];
  cancel: [];
}>();

// 响应式状态
const loading = ref(false);
const notification = ref<Notification | undefined>(props.notification);

// 计算属性
const modalTitle = computed(() => {
  return notification.value ? `通知详情 - ${notification.value.content.title}` : '通知详情';
});

const typeConfig = computed(() => {
  return notification.value ? getNotificationTypeConfig(notification.value.type) : null;
});

const categoryConfig = computed(() => {
  return notification.value ? getNotificationCategoryConfig(notification.value.category) : null;
});

const priorityConfig = computed(() => {
  return notification.value ? getNotificationPriorityConfig(notification.value.priority) : null;
});

const statusConfig = computed(() => {
  return notification.value ? getNotificationStatusConfig(notification.value.status) : null;
});

const parsedData = computed(() => {
  if (!notification.value?.content.data) return null;
  
  try {
    return JSON.parse(notification.value.content.data);
  } catch {
    return notification.value.content.data;
  }
});

// 方法
function formatTime(timeString: string): string {
  return new Date(timeString).toLocaleString('zh-CN');
}

async function handleMarkRead() {
  if (!notification.value) return;

  try {
    loading.value = true;
    await notificationApi.markAsRead(notification.value.id);
    
    // 更新本地状态
    notification.value.status = 1;
    notification.value.readAt = new Date().toISOString();
    
    message.success('已标记为已读');
  } catch (error) {
    console.error('Mark as read failed:', error);
    message.error('标记失败');
  } finally {
    loading.value = false;
  }
}

async function handleDelete() {
  if (!notification.value) return;

  try {
    loading.value = true;
    await notificationApi.deleteNotification(notification.value.id);
    
    message.success('删除成功');
    emit('ok');
  } catch (error) {
    console.error('Delete failed:', error);
    message.error('删除失败');
  } finally {
    loading.value = false;
  }
}

function handleOk() {
  emit('ok');
}

// 监听props变化
function setData(data: Notification) {
  notification.value = data;
}

// 暴露方法给父组件
defineExpose({
  setData,
});
</script>

<style scoped>
.notification-detail {
  @apply space-y-6;
}

.notification-detail__section {
  @apply border-b border-gray-100 pb-4 last:border-b-0;
}

.notification-detail__section-title {
  @apply text-lg font-medium text-gray-900 mb-3;
}

.notification-detail__grid {
  @apply grid grid-cols-2 gap-4;
}

.notification-detail__content {
  @apply space-y-4;
}

.notification-detail__item {
  @apply flex flex-col gap-1;
}

.notification-detail__item label {
  @apply text-sm font-medium text-gray-600;
}

.notification-detail__item span {
  @apply text-sm text-gray-900;
}

.notification-detail__message {
  @apply p-3 bg-gray-50 rounded-md text-sm text-gray-900 whitespace-pre-wrap;
}

.notification-detail__details {
  @apply p-3 bg-gray-50 rounded-md;
}

.notification-detail__details pre {
  @apply text-sm text-gray-900 whitespace-pre-wrap;
}

.notification-detail__data {
  @apply border border-gray-200 rounded-md overflow-hidden;
}

.notification-detail__recipients {
  @apply flex flex-wrap gap-1;
}

.notification-detail__actions {
  @apply pt-4 border-t border-gray-100;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-detail__grid {
    @apply grid-cols-1;
  }
}
</style>
