/**
 * 通知API服务
 * 基于项目现有的backendRequestClient进行API调用
 */

import type { NotificationApi } from './types';

import type {
  CreateNotificationFromTemplateRequest,
  CreateNotificationRequest,
  Notification,
  NotificationTemplate,
} from '#/types/notification';

import { backendRequestClient } from '#/api/request';

/**
 * 通知API服务类
 */
export class NotificationApiService {
  private readonly baseUrl = '/notifications';

  /**
   * 批量操作通知
   */
  async batchOperation(
    data: NotificationApi.BatchOperationRequest,
  ): Promise<NotificationApi.BatchOperationResponse> {
    const response =
      await backendRequestClient.post<NotificationApi.BatchOperationResponse>(
        `${this.baseUrl}/batch`,
        data,
      );
    return response;
  }

  /**
   * 通过模板创建通知
   */
  async createFromTemplate(
    data: CreateNotificationFromTemplateRequest,
  ): Promise<NotificationApi.CreateNotificationResponse> {
    const response =
      await backendRequestClient.post<NotificationApi.CreateNotificationResponse>(
        `${this.baseUrl}/from-template`,
        data,
      );
    return response;
  }

  /**
   * 创建通知
   */
  async createNotification(
    data: CreateNotificationRequest,
  ): Promise<NotificationApi.CreateNotificationResponse> {
    const response =
      await backendRequestClient.post<NotificationApi.CreateNotificationResponse>(
        this.baseUrl,
        data,
      );
    return response;
  }

  /**
   * 创建通知模板
   */
  async createTemplate(
    data: NotificationApi.CreateTemplateRequest,
  ): Promise<NotificationApi.CreateNotificationResponse> {
    const response =
      await backendRequestClient.post<NotificationApi.CreateNotificationResponse>(
        `${this.baseUrl}/templates`,
        data,
      );
    return response;
  }

  /**
   * 删除通知
   */
  async deleteNotification(
    id: string,
  ): Promise<NotificationApi.MarkAsReadResponse> {
    const response =
      await backendRequestClient.delete<NotificationApi.MarkAsReadResponse>(
        `${this.baseUrl}/${id}`,
      );
    return response;
  }

  /**
   * 删除通知模板
   */
  async deleteTemplate(
    id: string,
  ): Promise<NotificationApi.DeleteTemplateResponse> {
    const response =
      await backendRequestClient.delete<NotificationApi.DeleteTemplateResponse>(
        `${this.baseUrl}/templates/${id}`,
      );
    return response;
  }

  /**
   * 导出通知
   */
  async exportNotifications(
    data: NotificationApi.ExportNotificationsRequest,
  ): Promise<NotificationApi.ExportNotificationsResponse> {
    const response =
      await backendRequestClient.post<NotificationApi.ExportNotificationsResponse>(
        `${this.baseUrl}/export`,
        data,
      );
    return response;
  }

  /**
   * 获取通知详情
   */
  async getNotificationById(id: string): Promise<Notification> {
    const response =
      await backendRequestClient.get<NotificationApi.GetNotificationResponse>(
        `${this.baseUrl}/${id}`,
      );
    return response.notification;
  }

  /**
   * 获取用户通知列表
   */
  async getNotifications(
    params: NotificationApi.GetNotificationsParams = {},
  ): Promise<NotificationApi.GetNotificationsResponse> {
    const response =
      await backendRequestClient.get<NotificationApi.GetNotificationsResponse>(
        this.baseUrl,
        { params },
      );
    return response;
  }

  /**
   * 获取用户通知偏好设置
   */
  async getPreferences(): Promise<NotificationApi.GetPreferencesResponse> {
    const response =
      await backendRequestClient.get<NotificationApi.GetPreferencesResponse>(
        `${this.baseUrl}/preferences`,
      );
    return response;
  }

  /**
   * 获取通知统计
   */
  async getStatistics(
    params: NotificationApi.GetStatisticsParams = {},
  ): Promise<NotificationApi.GetStatisticsResponse> {
    const response =
      await backendRequestClient.get<NotificationApi.GetStatisticsResponse>(
        `${this.baseUrl}/statistics`,
        { params },
      );
    return response;
  }

  /**
   * 获取通知模板详情
   */
  async getTemplateById(id: string): Promise<NotificationTemplate> {
    const response =
      await backendRequestClient.get<NotificationApi.GetTemplateResponse>(
        `${this.baseUrl}/templates/${id}`,
      );
    return response.template;
  }

  /**
   * 获取通知模板列表
   */
  async getTemplates(
    params: NotificationApi.GetTemplatesParams = {},
  ): Promise<NotificationApi.GetTemplatesResponse> {
    const response =
      await backendRequestClient.get<NotificationApi.GetTemplatesResponse>(
        `${this.baseUrl}/templates`,
        { params },
      );
    return response;
  }

  /**
   * 获取未读通知数量
   */
  async getUnreadCount(): Promise<number> {
    const response =
      await backendRequestClient.get<NotificationApi.GetUnreadCountResponse>(
        `${this.baseUrl}/unread-count`,
      );
    return response.unreadCount;
  }

  /**
   * 标记所有通知为已读
   */
  async markAllAsRead(): Promise<NotificationApi.MarkAsReadResponse> {
    const response =
      await backendRequestClient.put<NotificationApi.MarkAsReadResponse>(
        `${this.baseUrl}/read-all`,
      );
    return response;
  }

  /**
   * 标记通知为已读
   */
  async markAsRead(id: string): Promise<NotificationApi.MarkAsReadResponse> {
    const response =
      await backendRequestClient.put<NotificationApi.MarkAsReadResponse>(
        `${this.baseUrl}/${id}/read`,
      );
    return response;
  }

  /**
   * 订阅通知
   */
  async subscribe(
    data: NotificationApi.SubscribeRequest,
  ): Promise<NotificationApi.SubscribeResponse> {
    const response =
      await backendRequestClient.post<NotificationApi.SubscribeResponse>(
        `${this.baseUrl}/subscribe`,
        data,
      );
    return response;
  }

  /**
   * 取消订阅通知
   */
  async unsubscribe(
    data: NotificationApi.UnsubscribeRequest,
  ): Promise<NotificationApi.UnsubscribeResponse> {
    const response =
      await backendRequestClient.post<NotificationApi.UnsubscribeResponse>(
        `${this.baseUrl}/unsubscribe`,
        data,
      );
    return response;
  }

  /**
   * 更新用户通知偏好设置
   */
  async updatePreferences(
    data: NotificationApi.UpdatePreferencesRequest,
  ): Promise<NotificationApi.MarkAsReadResponse> {
    const response =
      await backendRequestClient.put<NotificationApi.MarkAsReadResponse>(
        `${this.baseUrl}/preferences`,
        data,
      );
    return response;
  }

  /**
   * 更新通知模板
   */
  async updateTemplate(
    data: NotificationApi.UpdateTemplateRequest,
  ): Promise<NotificationApi.MarkAsReadResponse> {
    const response =
      await backendRequestClient.put<NotificationApi.MarkAsReadResponse>(
        `${this.baseUrl}/templates/${data.id}`,
        data,
      );
    return response;
  }

  /**
   * 上传附件
   */
  async uploadFile(
    data: NotificationApi.FileUploadRequest,
  ): Promise<NotificationApi.FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', data.file);
    if (data.category !== undefined) {
      formData.append('category', data.category.toString());
    }
    if (data.description) {
      formData.append('description', data.description);
    }

    const response =
      await backendRequestClient.post<NotificationApi.FileUploadResponse>(
        `${this.baseUrl}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
    return response;
  }
}

// 导出单例实例
export const notificationApi = new NotificationApiService();

// 导出类型
export type { NotificationApi };

// 导出便捷方法
export const {
  getNotifications,
  getNotificationById,
  getUnreadCount,
  createNotification,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  batchOperation,
  getTemplates,
  getTemplateById,
  createFromTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  getStatistics,
  getPreferences,
  updatePreferences,
  exportNotifications,
  subscribe,
  unsubscribe,
  uploadFile,
} = notificationApi;
