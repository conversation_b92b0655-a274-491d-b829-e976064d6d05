/**
 * 通知组件类型定义
 */

import type { Notification } from '#/types/notification';

/**
 * 通知中心组件Props
 */
export interface NotificationCenterProps {
  /** 最大高度 */
  maxHeight?: number;
  /** 是否显示分类标签 */
  showCategories?: boolean;
  /** 是否自动刷新 */
  autoRefresh?: boolean;
  /** 自动刷新间隔（毫秒） */
  refreshInterval?: number;
  /** 是否显示清空按钮 */
  showClearButton?: boolean;
  /** 是否显示全部已读按钮 */
  showMarkAllButton?: boolean;
}

/**
 * 通知列表组件Props
 */
export interface NotificationListProps {
  /** 通知列表 */
  notifications: Notification[];
  /** 是否加载中 */
  loading?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 是否显示操作按钮 */
  showActions?: boolean;
  /** 是否显示头像 */
  showAvatar?: boolean;
  /** 是否显示时间 */
  showTime?: boolean;
  /** 是否显示分类标签 */
  showCategory?: boolean;
  /** 最大显示数量 */
  maxItems?: number;
}

/**
 * 通知项组件Props
 */
export interface NotificationItemProps {
  /** 通知数据 */
  notification: Notification;
  /** 是否显示操作按钮 */
  showActions?: boolean;
  /** 是否显示头像 */
  showAvatar?: boolean;
  /** 是否显示时间 */
  showTime?: boolean;
  /** 是否显示分类标签 */
  showCategory?: boolean;
  /** 是否可点击 */
  clickable?: boolean;
}

/**
 * 通知中心事件
 */
export interface NotificationCenterEvents {
  /** 点击通知项 */
  'notification-click': [notification: Notification];
  /** 标记已读 */
  'mark-read': [id: string];
  /** 标记全部已读 */
  'mark-all-read': [];
  /** 删除通知 */
  'delete': [id: string];
  /** 清空所有通知 */
  'clear-all': [];
  /** 加载更多 */
  'load-more': [];
  /** 刷新 */
  'refresh': [];
}

/**
 * 通知列表事件
 */
export interface NotificationListEvents {
  /** 点击通知项 */
  'notification-click': [notification: Notification];
  /** 标记已读 */
  'mark-read': [id: string];
  /** 删除通知 */
  'delete': [id: string];
  /** 加载更多 */
  'load-more': [];
}

/**
 * 通知项事件
 */
export interface NotificationItemEvents {
  /** 点击通知项 */
  'click': [notification: Notification];
  /** 标记已读 */
  'mark-read': [id: string];
  /** 删除通知 */
  'delete': [id: string];
}

/**
 * 通知显示选项
 */
export interface NotificationDisplayOptions {
  /** 是否显示头像 */
  showAvatar: boolean;
  /** 是否显示时间 */
  showTime: boolean;
  /** 是否显示分类 */
  showCategory: boolean;
  /** 是否显示操作按钮 */
  showActions: boolean;
  /** 时间格式 */
  timeFormat: 'relative' | 'absolute';
  /** 最大内容长度 */
  maxContentLength: number;
}

/**
 * 通知过滤选项
 */
export interface NotificationFilterOptions {
  /** 状态过滤 */
  status?: number[];
  /** 分类过滤 */
  categories?: number[];
  /** 类型过滤 */
  types?: number[];
  /** 关键词过滤 */
  keyword?: string;
  /** 时间范围过滤 */
  dateRange?: [string, string];
}

/**
 * 通知排序选项
 */
export interface NotificationSortOptions {
  /** 排序字段 */
  field: 'createdAt' | 'priority' | 'status';
  /** 排序方向 */
  order: 'asc' | 'desc';
}

/**
 * 通知操作选项
 */
export interface NotificationActionOptions {
  /** 是否显示标记已读按钮 */
  showMarkRead: boolean;
  /** 是否显示删除按钮 */
  showDelete: boolean;
  /** 是否显示详情按钮 */
  showDetail: boolean;
  /** 自定义操作按钮 */
  customActions?: Array<{
    key: string;
    label: string;
    icon?: string;
    handler: (notification: Notification) => void;
  }>;
}

/**
 * 通知主题配置
 */
export interface NotificationThemeConfig {
  /** 主色调 */
  primaryColor: string;
  /** 成功色 */
  successColor: string;
  /** 警告色 */
  warningColor: string;
  /** 错误色 */
  errorColor: string;
  /** 信息色 */
  infoColor: string;
  /** 边框圆角 */
  borderRadius: string;
  /** 阴影 */
  boxShadow: string;
}

/**
 * 通知动画配置
 */
export interface NotificationAnimationConfig {
  /** 进入动画 */
  enterAnimation: string;
  /** 离开动画 */
  leaveAnimation: string;
  /** 动画持续时间 */
  duration: number;
  /** 动画缓动函数 */
  easing: string;
}

/**
 * 通知响应式配置
 */
export interface NotificationResponsiveConfig {
  /** 移动端断点 */
  mobileBreakpoint: number;
  /** 平板端断点 */
  tabletBreakpoint: number;
  /** 桌面端断点 */
  desktopBreakpoint: number;
  /** 移动端配置 */
  mobile: Partial<NotificationDisplayOptions>;
  /** 平板端配置 */
  tablet: Partial<NotificationDisplayOptions>;
  /** 桌面端配置 */
  desktop: Partial<NotificationDisplayOptions>;
}
